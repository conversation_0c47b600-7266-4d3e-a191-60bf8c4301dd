#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
问题分析API调用函数
"""

import json
import requests
from typing import Dict, Any, Optional
import sys
import os
from datetime import date

# 添加项目根路径到系统路径
root_path = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
sys.path.append(root_path)

try:
    from steps.tools import get_prompt, qwen72B_sli
except ImportError as e:
    print(f"导入模块失败: {e}")
    get_prompt = None
    qwen72B_sli = None


def analyze_question(question: str) -> Optional[Dict[str, Any]]:
    """
    调用问题分析API
    
    Args:
        question: 用户问题
        
    Returns:
        API返回的JSON结果，失败时返回None
    """
    url = 'http://192.168.219.106:8086/analyzeQuestion'
    headers = {
        'Content-Type': 'application/json'
    }
    params = {
        "params": {
            "question": question
        }
    }
    
    try:
        response = requests.post(
            url=url,
            data=json.dumps(params),
            headers=headers,
            verify=False,
            timeout=30
        )
        response.raise_for_status()
        return response.json()
        
    except Exception as e:
        print(f"API调用失败: {e}")
        return None


def get_mql_from_metrics(question: str, metrics: Dict[str, Any]) -> Optional[Dict[str, Any]]:
    """
    基于metrics生成MQL查询语句

    Args:
        question: 用户问题
        metrics: 从API获取的metrics数据

    Returns:
        生成的MQL查询对象，失败时返回None
    """
    if not get_prompt or not qwen72B_sli:
        print("大模型工具未导入，无法生成MQL")
        return None

    try:
        # 将metrics转换为字符串格式，用于prompt
        metrics_str = json.dumps(metrics, ensure_ascii=False, indent=2)

        messages = [
            {
                "role": "user",
                "content": get_prompt('MQL.txt', question, '', '', '', metrics_str, str(date.today()))
            }
        ]

        response = qwen72B_sli(messages)
        jsonstr = response.replace("```", "").replace("json", "")

        mql_result = json.loads(jsonstr)
        print("生成的MQL:")
        print(json.dumps(mql_result, indent=4, ensure_ascii=False))

        return mql_result

    except Exception as e:
        print(f"MQL生成失败: {e}")
        return None


def analyze_question_and_generate_mql(question: str) -> Dict[str, Any]:
    """
    完整流程：调用API分析问题并生成MQL

    Args:
        question: 用户问题

    Returns:
        包含API结果和MQL的完整结果
    """
    result = {
        'question': question,
        'api_result': None,
        'mql': None,
        'success': False,
        'error': None
    }

    try:
        # 步骤1: 调用API分析问题
        print(f"分析问题: {question}")
        api_result = analyze_question(question)

        if not api_result:
            raise Exception("API调用失败")

        result['api_result'] = api_result

        # 检查API返回状态
        if api_result.get('status', {}).get('code') != '1':
            raise Exception(f"API返回错误: {api_result.get('status', {}).get('text', '未知错误')}")

        # 步骤2: 提取metrics并生成MQL
        custom_data = api_result.get('custom', {})
        if 'metrics' in custom_data:
            print("生成MQL...")
            mql = get_mql_from_metrics(question, custom_data)
            result['mql'] = mql

            if mql:
                result['success'] = True
            else:
                result['error'] = "MQL生成失败"
        else:
            result['error'] = "API返回数据中没有metrics"

    except Exception as e:
        result['error'] = str(e)
        print(f"处理失败: {e}")

    return result


def test_api():
    """测试API调用"""
    test_question = "风险啊啊啊标段数量近期"

    print(f"测试问题: {test_question}")
    result = analyze_question(test_question)

    if result:
        print("API调用成功!")
        print(json.dumps(result, ensure_ascii=False, indent=2))
    else:
        print("API调用失败!")


def test_full_process():
    """测试完整流程"""
    test_question = "风险啊啊啊标段数量近期"

    print("=" * 60)
    print("测试完整流程：API分析 + MQL生成")
    print("=" * 60)

    result = analyze_question_and_generate_mql(test_question)

    print(f"\n最终结果:")
    print(f"成功: {result['success']}")
    if result['error']:
        print(f"错误: {result['error']}")
    if result['mql']:
        print(f"MQL已生成")


if __name__ == "__main__":
    print("1. 测试API调用")
    print("2. 测试完整流程")

    choice = input("请选择测试模式 (1/2): ").strip()

    if choice == "2":
        test_full_process()
    else:
        test_api()
