import asyncio
import time

import aiohttp

import time

import requests
import datetime
import json
def test(prompt):
    url = "http://10.132.36.149/wechat/talkv2"
    headers = {"Content-Type": "application/json","Authorization":"Bearer 520f93241d15138ce7abf53b9edf6a8d-zwbgsoa"}
    payload = {
        "sender": "123213213dadsa",
        "message": f"deepseek({prompt})",
        "appguid": "ad6d7a67-a890-42af-adda-9d228b019f8b",
        "debug": False,
        "stream": True,
        "streamagent":True
    }

    # 请求接口获取数据
    def fetch_talk_response():
        try:
            # 根据接口需求构造请求数据（这里使用空JSON）
            response = requests.post(url, json=payload, headers=headers)

            # 检查响应状态
            if response.status_code == 200:
                print(response.content)
                # print(response.json())
                return response.json()
            else:
                print(f"Failed to fetch data, HTTP status code: {response.status_code}")
                return None
        except requests.RequestException as e:
            print(f"Request failed: {e}")
            return None

    # 提取content字段的值
    def get_content_url(data):
        try:
            if 'result' in data:
                data_json = json.loads(data['result'])
                return data_json['url']
                # for item in data['result']:
                #     return item['content']
            return None  # 如果未找到content字段
        except KeyError as e:
            print(f"KeyError: {e}")
            return None

    try:
        response_data = fetch_talk_response()
        content_url = ''
        if response_data:
            # 提取content字段中的URL
            content_url = get_content_url(response_data)
        print(content_url)
        # content_url = content_url.replace('https://laydb.ahzwfw.gov.cn:8090', 'http://192.168.140.59:8770/blbb')
        if not content_url:
            print(response_data)
            return None
        start_time = time.time()
        print(datetime.datetime.now())
        with requests.post(content_url, json=None, headers=None, stream=True, verify=False) as response:
            print(f"Response Content-Type: {response.headers.get('Content-Type')}")
            print(f"Response Transfer-Encoding: {response.headers.get('Transfer-Encoding')}")

            if response.status_code == 200:
                print("Connection established, streaming data...")
                for line in response.iter_lines(decode_unicode=True):
                    if line:  # Avoid processing empty lines
                        print(f"[{datetime.datetime.now()}] {line}")
            else:
                print(f"Failed to connect: {response.status_code}, {response.reason}")
        print(time.time() -start_time)
    except Exception as e:
        print(f"An error occurred: {e}")


if __name__ == "__main__":
    prompt="""给我一段10个子的话"""
    test(prompt)