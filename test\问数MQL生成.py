import json

import requests

from steps.tools import qwen72B,qwenMax2,qwen14B,qwen3,get_prompt,qwen72B_sli

import re
import yaml
from collections import defaultdict

from MQLtoSQLConverter import MQLtoSQLConverter
from datetime import date

def getMetric(question):
    url='http://192.168.212.113:9081/epoint-AIChat-web/rest/aichat/getParsedS2SQL'
    headers = {'Content-Type': 'application/json','Authorization': 'Basic admin admin'}
    params={
        "question":question,
        "appguid":"adc259a6-9c96-440c-9d34-48ec5e1135bf"
    }
    response = requests.post(url=url, data=json.dumps(params), headers=headers, verify=False)
    results = response.text
    return json.loads(results)

def parse_schema_text(text: str):
    # 提取数据库类型和表名
    db_type_match = re.search(r"<DatabaseType=(\w+), Table=([^\s,]+)", text)
    db_type, table_name = db_type_match.groups() if db_type_match else (None, None)

    # 提取 Metrics
    metrics_raw = re.findall(r"<(.*?)AGGREGATE:'(.*?)'(.*?)>", text)
    metrics = []
    for m in metrics_raw:
        name_alias_comment = m[0].strip()
        agg_func = m[1].strip()
        trailing = m[2].strip()

        # 提取name
        name = re.split(r"\s+", name_alias_comment)[0]

        # 提取 ALIAS
        alias_match = re.search(r"ALIAS\s+([^\|>]+)", name_alias_comment + trailing)
        alias = [alias.strip() for alias in alias_match.group(1).split('|')] if alias_match else [name]

        # 提取 COMMENT
        comment_match = re.search(r"COMMENT\s+''(.*?)''", name_alias_comment + trailing)
        comment = comment_match.group(1) if comment_match else ""

        metrics.append({
            "name": name,
            "alias": list(set(alias + [name])),
            "aggregate": agg_func,
            "comment": comment
        })

    # 提取 Dimensions
    dims_raw = re.findall(r"<([^<>]+?)(?:\s+ALIAS\s+([^|>]+))?>", text)
    dimensions = []
    for d in dims_raw:
        name = d[0].strip()
        aliases = [d[1].strip()] if d[1] else []

        # 提取 COMMENT
        comment_match = re.search(r"COMMENT\s+''(.*?)''", d[0])
        comment = comment_match.group(1) if comment_match else ""

        dim = {"name": name, "comment": comment}
        if aliases:
            dim["alias"] = list(set(aliases + [name]))
        dimensions.append(dim)

    # 提取 DomainTerms
    domain_terms = []
    domain_raw = re.findall(r"<([^<>]+?)\s+COMMENT\s+([^<>]+?)>", text)
    for name, comment in domain_raw:
        domain_terms.append({
            "name": name.strip(),
            "comment": comment.strip()
        })

    return {
        "DatabaseType": db_type,
        "Table": table_name,
        "PrimaryKeyField": [],
        "PartitionTimeField": [],
        "Metrics": metrics,
        "Dimensions": dimensions,
        "FilterValues": {},
        "DomainTerms": domain_terms
    }

def extract_text_code_blocks(markdown_text):
    pattern = r"```text\s*([\s\S]*?)\s*```"
    match = re.search(pattern, markdown_text, re.DOTALL)
    if match:
        return match.group(1).strip()
    return None

def getMQL(question,parsed):
    messages = [
        {
            "role": "user",
            "content":get_prompt('MQL.txt',question,'','','',parsed,str(date.today()))
        }
    ]
    response = qwen72B_sli(messages)
    jsonstr = response.replace("```","").replace("json","")
    print(json.dumps(json.loads(jsonstr) , indent=4,ensure_ascii=False))
    return json.loads(jsonstr)

if __name__=="__main__":
    queryList = [
        # "2024年工程类项目成交数量是多少，占比是多少",
        # "2024年工程类项目成交金额是多少，占比是多少",
        # "2024年工程类项目成交数量是多少，成交数量占比是多少，中标金额时多少，中标金额占比是多少",
        # "2024年1月工程类项目成交数量是多少",
        # "2024年1月工程类项目成交数量是多少，同比是多少",
        # "2024年12月工程类项目成交数量是多少，环比是多少",
        # "近12个月的项目成交数量趋势",
        "2024年各项目类型的成交数量是多少，按照成交数量降序排序，来个折线图",
        # "2024年成交项目数量最多的项目类型是什么？"
    ]
    for question in queryList:
        try:
            # metricjson = getMetric(question)
            # llm_query = metricjson['custom']['llm_query']
            # parsed = extract_text_code_blocks(llm_query)
            # print("question:",question)
            # print("parsed:",parsed)
            MQL=getMQL(question,"")
            print("MQL:",MQL)
            # mql_str = json.dumps(
            #     MQL,
            #     indent=2,  # 缩进增强可读性
            #     ensure_ascii=False
            # )
            # print("MQL:\n",mql_str)
            # converter = MQLtoSQLConverter()
            # sqllist=converter.convert(MQL)
            # print(sqllist)
            print("==========================================================")
        except Exception as e:
            print("question:",question,"error!!!",e)