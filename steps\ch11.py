# -*- coding: utf-8 -*-
"""
@Des     : 根据关联字，检索B类问题
"""
import datetime
import json
import math
import re
import time
from uuid import uuid4

import requests
from requests.models import HTTPBasicAuth

import urllib3

urllib3.disable_warnings(urllib3.exceptions.InsecureRequestWarning)


class Restful(object):

    def __init__(self, url):

        self.url = url

    def get_results(self, data):
        headers = {
            'Content-Type': 'application/json',
            "User-Agent": "python",
            "Authorization": "Bearer 4b1f2daed98265cc43aa7ebccfa89f0e",
        }
        response = requests.post(url=self.url, data=json.dumps(data), headers=headers, verify=False)

        results = response.text

        return json.loads(results)


def callES(question,index_name,select_field,fields,num):
    inteligentsearch_url = "http://192.168.186.65/inteligentsearch/rest"
    params = {
        "wd": question,
        "fields": select_field,
        "re_fields": fields,
        "vecfield": "contentvec",
        "cnums": index_name,
        "pn": 0,
        "rn": num,
        "vecType": "1",
        "pluginName": "EmbeddingOperatorNew",
        "isSynonym": "1",
        "esdsid": 8,
        "accuracy": 15,
        "rescore": "{'inteligentsearchType':'3','sim1':'kworg'}",
        "opCondition": None,
        "opType": "0"
    }

    headers = {
        'Content-Type': 'application/json',
        "Authorization": "Bearer " + "token",
        "User-Agent": "python"
    }
    response = requests.post(url=inteligentsearch_url + "/esinteligentsearch/getByVector",
                                data=json.dumps(params), headers=headers)
    re = json.loads(response.text)
    results = eval(json.dumps(json.loads(re["content"])["result"]["records"]))
    return results

def query(question):
    index_name = "nzyxzrgs19"  #库名，需要变
    result_retrieve1 = callES(question, index_name,"keywords","content1;content2;keywords;dbguid",10)
    result_retrieve2 = callES(question, index_name,"content2_text","content1;content2;keywords;dbguid",10)
    return result_retrieve1 + result_retrieve2  

def queryB(question,keywords):
    reranker_url = "http://192.168.186.112:8080/epoint-gateway/linezhengshi_reranker/predict"
    reranker_engine = Restful(reranker_url) 
    index_name = "nzyxzrgs19"  #库名，需要变

    result_retrieve1 = callES(keywords, index_name,"kw","kw;path;content",10)
    result_retrieve2 = callES(keywords, index_name,"content","kw;path;content",10)

    lst = result_retrieve1 + result_retrieve2
    
    text_list = [pre["content"] for pre in lst]
    data_reranker = {"text": question, "compare_list": text_list}
    reranker_results = reranker_engine.get_results(data_reranker)
    results_reranker = reranker_results["result"]

    final_results = []
    for i, re_reranker in enumerate(results_reranker):
        content, score = re_reranker[0], re_reranker[1]
        item = lst[text_list.index(content)]
        final_results.append(item)

    num = 0
    content = ""
    for item in result_retrieve1 + result_retrieve2:
        num += 1
        if num >3:
            break
        if "content" in item:
            content += item["path"] + "\n" + item["content"] + "\n\n"
    return content

if __name__ == "__main__":

    # 数据查询
    question = "我司注册地址为深圳市福田区福田街道，为外商独资企业，现已经办结一笔股权出质登记，后续拟办理该笔股权出质登记的注销，并新设一笔股权出质登记。请问：深圳市的预约系统中，是否在注销登记完成的当天或之前即可办理新设登记的预约？"
    keywords = "办理方式 注销登记 新设登记"
    content = queryB(question,keywords)
    print(content)
    print(len(content))