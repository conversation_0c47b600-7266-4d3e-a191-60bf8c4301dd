# -*- coding: utf-8 -*-
"""
Description:

@Author: ssjie

Created on: 2024/12/3
"""
import time
import json
import requests
import base64


url = "http://*************:30012/gateway/deepseek/v1/chat/completions"
messages = [
    {
        "role": "user",
        "content": f"""生成50字文章"""
    }
]


data = {
    "messages": messages,
    "stream": False,
    "model":"deepseekr1"
}

response = requests.request("POST", url, data=json.dumps(data), stream=False)

print(json.loads(response.text)['choices'][0]['message']['content'])
