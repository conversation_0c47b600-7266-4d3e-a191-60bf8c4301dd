# 将用户的问题，拆分为寒喧、情绪、业务问题

# TODO 调一调提示词

from tools import get_prompt16, qwenMax2

# question = "内资企业怎么注销"
# question = "你好"
# question = "你们办理效率太低了"
# question = "你们办理效率太低了，内资企业怎么注销"   
question = "注销后的公司名称，多久之后可以再重新注册。例如：深圳甲虫网络科技有限公司 ，现在核名不通过。"
test1 = "['内资有限责任公司及分公司','外商投资有限责任公司及分公司','内资股份有限公司','外商投资股份有限公司','个人独资企业及分支机构','外商投资合伙企业及分支机构','合伙企业及分支机构','股份合作公司及分公司','非公司企业法人及分支机构','非公司外商投资及分支机构','营业单位','个体工商户','外国地区企业常驻代表机构及从事经营活动']";
test2 = "['备案','变更','设立分公司','注销','歇业','设立分支机构','营业执照','变更分公司','变更分支机构','注销分支机构','备案分支机构','股权出质','分立','合并']";
messages = [
    {
        "role": "system",
        "content": get_prompt16("ch16.txt",question,test1,test2)
    }
]

response = qwenMax2(messages)

print(response)

#总控模块，在拆出业务问题时就拆出多个问题