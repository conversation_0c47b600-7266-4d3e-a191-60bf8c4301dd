#导库，将原先的导入到新库中

import requests
import json
from requests.auth import HTTPBasicAuth
import urllib3

# 关闭 HTTPS 安全警告
urllib3.disable_warnings(urllib3.exceptions.InsecureRequestWarning)

def tranpage(pagenum):
    # Elasticsearch 查询接口
    search_url = "https://192.168.186.63:9200/zntp/_search"
    search_headers = {
        "Content-Type": "application/json"
    }
    auth = HTTPBasicAuth('admin', 'admin')  # Basic Auth

    search_body = {
        "from": pagenum*20,
        "size": 20,
        "query": {
            "match_all": {}
        },
        "highlight": {
            "pre_tags": [
                "<em style='color:red'>"
            ],
            "post_tags": [
                "</em>"
            ],
            "require_field_match": True,
            "fields": {
                "title_ngram": {}
            }
        }
    }

    # 关闭证书验证，仅用于测试环境
    response = requests.post(search_url, headers=search_headers, auth=auth, json=search_body, verify=False)
    hits = response.json().get("hits", {}).get("hits", [])

    # 插入接口地址
    insert_url = "http://localhost:9060/rag/embed"
    insert_headers = {
        "Content-Type": "application/json"
    }
    index_name = "94407bb6-b00f-4b43-8223-c392e617c9d5"

    contentinfo_list = []
    for i, hit in enumerate(hits):
        source = hit.get("_source", {})
        content_text = source.get("content_text", "")
        if not content_text.strip():
            continue  # 跳过空文本
        
        item = {
            "text": content_text,
            "tags": source.get("kworg_keyword", ""),
            "extra_data": {
                "purpose": {"value": source.get("purpose_keyword", "")},
                "subject": {"value": source.get("ptype_keyword", "")},
                "situation": {"value": ""},
                "domain": {"value": source.get("domainname_keyword", "")}
            }
        }
        json.dumps(item,ensure_ascii=True)
        contentinfo_list.append(item)

    payload = {
        "contentinfo": contentinfo_list,
        "index_name": index_name
    }

    insert_response = requests.post(insert_url, headers=insert_headers, json=payload)
    if insert_response.status_code != 200:
        print(f"插入失败 ID: 错误: {insert_response.text}")


for i in range(1,330):
    tranpage(i)
    print("finised page",i)