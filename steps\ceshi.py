import json
import base64
content = "{\"知识领域\":\"市场主体登记领域\",\"确认主体类型\":\"内资股份有限公司\",\"确认注销类型\":\"无需识别\"}"
#fileContent = getSlot(`fileContent`)
#换了方式用base64切过来
# 解码Base64字符串为字节数据
fileContent = base64.b64decode("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")
historyElements = ""
parsed_result = {}
try:
    content = content[content.index("{"):content.rindex("}")+1]
    parsed_result = json.loads(content)
except Exception as e:
    None
domain = '无法判断您的知识领域'
isComplete = '0'
elements = {}
missingElements = []
if '知识领域' in parsed_result:
    domain = parsed_result['知识领域']
if domain != '无法判断您的知识领域':
    # 这里就不校验domain的正确性了
    elements['知识领域'] = domain
    # 这里就不校验元素的缺失了
    for key, value in parsed_result.items():
        if key != '知识领域':
            if value != '需要询问' and value != '无需识别':
                # 这里也不校验元素名称的正确性了
                elements[key] = value
            elif value == '需要询问':
                for entity in fileContent["knowledgeDomains"]:
                    if entity['name'] == domain:
                        for information in entity['informationelements']:
                            if information["name"] == key:
                                if historyElements and domain in historyElements and key in historyElements[domain]:
                                    elements[key] = historyElements[domain][key]
                                else:
                                    missingElements.append({"name": key, "desc": entity['description']})
                            break
    if len(missingElements) == 0:
        isComplete = "1"

