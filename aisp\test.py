import os
import base64

import requests
from openai import OpenAI
from pathlib import Path

def encode_image_to_base64(image_path):
    """将本地图片编码为base64格式"""
    with open(image_path, "rb") as image_file:
        return base64.b64encode(image_file.read()).decode('utf-8')

def analyze_image_with_qwen_vl(image_path, question="""# 目的 你是一个图像识别和政务服务事项知识的业务专家，请按照后续要求对附件完成信息识别工作。 # 变量X 设变量X的初始值为0 # 识别区域 附件图片底部区域中**建设单位意见**文字的右侧 # 识别内容 请识别区域内是否有清晰可辨的**个人签名**（包括但不限于个人签名、手写名字等）或者**个人姓名盖章**（即带有个人姓名的印章），当识别到时，将识别到的数量赋值给X # 结果赋值 返回结果中name使用"材料签名检测"，value使用X的值。 # 结果返回 1.必须以JSON形式放在JSON代码块中返回，无需返回分析过程或解释 2.返回格式参考： { "msginfo": [{ "name": "材料签名检测", "value": "X" } ] }"""):

    # 检查图片文件是否存在
    if not os.path.exists(image_path):
        return {"error": f"图片文件不存在: {image_path}"}

    try:
        # 将图片编码为base64
        base64_image = encode_image_to_base64(image_path)

        # client = OpenAI(
        #     api_key="sk-b20c97b733bd468fbbab70086e8aaf6b",
        #     base_url="https://dashscope.aliyuncs.com/compatible-mode/v1",
        # )
        #
        # completion = client.chat.completions.create(
        #     model="qwen2.5-vl-72b-instruct",  # 此处以qwen-vl-plus为例，可按需更换模型名称
        #     messages=[{
        #         "role": "user",
        #         "content": [
        #             {
        #                 "type": "image_url",
        #                 "image_url": {"url": f"data:image/jpeg;base64,{base64_image}"}
        #             },
        #             {
        #                 "type": "text",
        #                 "text": question
        #             }
        #         ]
        #     }]
        # )

        url = "http://192.168.210.134:8888/EpointFrame/rest/dynamicapi/epointqw72vl"
        headers = {"Content-Type": "application/json","cookie": "_font_size_ratio_=1.0; sid=D5D0AC2786284255A158A7EB7A0F6961; epoint_local=zh_CN; EPTOKEN=9E742E94513CE9453552A76FEC08E1856B56604CC6FFDB39DC098EB6A4E2DA3F; _idea_skin_=cyanine; _theme_=idea"}
        # ,
        payload = {
            "stream":False,
            "messages":[{
                "role": "user",
                "content": [
{
"type": "text",
"text": "# 目的 你是一个图像识别和政务服务事项知识的业务专家，请按照后续要求对附件完成信息识别工作。 # 变量X 设变量X的初始值为0 # 识别区域 附件图片底部区域中**建设单位意见**文字的右侧 # 识别内容 请识别区域内是否有清晰可辨的**个人签名**（包括但不限于个人签名、手写名字等）或者**个人姓名盖章**（即带有个人姓名的印章），当识别到时，将识别到的数量赋值给X # 结果赋值 返回结果中name使用\"材料签名检测\"，value使用X的值。 # 结果返回 1.必须以JSON形式放在JSON代码块中返回，无需返回分析过程或解释 2.返回格式参考： { \"msginfo\": [{ \"name\": \"材料签名检测\", \"value\": \"X\" } ] }"
},
{
"type": "image_url",
"image_url": {
"url": "https://zwfwys.epoint.com.cn:8443/jn-zwfw/rest/attachAction/getContent?isCommondto=true&attachGuid=ba78e6ec-1f73-4e8a-b48b-cb004f4d7c89"
}
}
]
            }],
            "temperature":0.7
        }
        response = requests.post(url, json=payload, headers=headers)

        return {
            "success": True,
            "image_path": image_path,
            "question": question,
            "response": response.json(),
            # "model": completion.model,
            # "usage": completion.usage.model_dump() if completion.usage else None
        }

    except Exception as e:
        return {
            "success": False,
            "error": str(e),
            "image_path": image_path
        }

def main():
    # 获取当前脚本所在目录
    current_dir = Path(__file__).parent
    image_dir = current_dir / "image"

    # 检查image文件夹是否存在
    if not image_dir.exists():
        print(f"错误：image文件夹不存在: {image_dir}")
        return

    # 支持的图片格式
    supported_formats = {'.jpg', '.jpeg', '.png', '.bmp', '.gif', '.webp'}

    # 获取所有图片文件
    image_files = [f for f in image_dir.iterdir()
                   if f.is_file() and f.suffix.lower() in supported_formats]

    if not image_files:
        print(f"在 {image_dir} 中没有找到支持的图片文件")
        return

    print(f"找到 {len(image_files)} 个图片文件，开始分析...")
    print("=" * 60)

    # 逐个分析图片
    for i, image_file in enumerate(image_files, 1):
        print(f"\n[{i}/{len(image_files)}] 正在分析: {image_file.name}")
        print("-" * 40)

        # 调用视觉模型分析图片
        result = analyze_image_with_qwen_vl(str(image_file))

        if result.get("success"):
            print(f"分析结果: {result['response']}")
        else:
            print(f"分析失败: {result.get('error', '未知错误')}")

        print("-" * 40)

if __name__ == "__main__":
    main()