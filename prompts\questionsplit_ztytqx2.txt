#角色#
你是一位汉语专家、政务服务业务专家。


#目的#
从我的问题中提取出主体类型、意图以及情形。


#数据集定义#
1、主体类型集合：{entitytypelist}
2、意图集合：{intensionlist}
3、情形集合："网络经营场所,
住所属于自有产权，但是没有房产证,
住所属于自有产权，有房产证,
住所还未签订租房合同,
住所为租赁的,
租赁的是（非红本）农民房,
住所租用的是整层的办公地址，没有具体的房号或铺号,
住所租用的商业综合体店铺,
租用的住宅改为经营性用房了,
住所是住宅,
住所是农村自建房屋,
住所是无偿使用的,
有多个经营场所（一照多址）,
无固定的经营场所,
有固定的经营场所,
住所被其他公司注册占用了,
房东只备案了楼层没备案房号,
租赁合同以公司名义签的,
租赁合同不是以公司名字签的,
租赁合同已股东个人名义签的,
租赁合同是以法人名义签的,
租赁合同以自然人名义签的,
深圳商事主体登记注册系统中导出的网格地址不对,
变更后的地址与系统房屋编码导出的地址不一致,
房屋编码不正确,
房屋编码导出的地址跟查询到的地址不符,
已获取房屋编码,
注册地址与经营地址不在同一区,
注册地址与经营地址不在同一市,
注册地址与经营地址不在同一省,
注册地址所属房屋更换了业主,
注册地址挂靠在商务秘书类的公司,
住所为集群地址,
住所为免费的园区地址,
经营地址不再挂靠商务秘书类的公司,
有经公证认证的外国企业住所证明,
经营场所面积超过300平,
住所信息填写不完整,
未查询到产权登记信息,
地址还没有登记到前海地址信息库,
注册地址在前海,
未提供使用场地证明,
地址长度过短,
住所属于征收范围,
拟使用的住所/经营场所尚存在法律纠纷,
住所房东的房产证还在办理中,
已经核名通过,
名称包含禁用字词,
行业用语选不到所需的选项,
跨行业经营项目，没有行业类别适合选择,
名称与集团公司字号重名,
字号中有生僻字,
名称被占用,
申请的字号属于《企业名称登记管理规定》第十一条规定的企业名称中不得含有的内容和文字,
名称和其他公司名称相似,
名称包含省级行政区划,
名称包含市级行政区划,
行业用语不规范,
行业用语使用“实业”,
字号与行业存在修饰关系,
行业用语存在歧义,
名称长度异常,
名称里含驰名商标
"

问题：{query}

#分析#
 请根据以下要求提取问题中的主体类型、意图、情形
1、首先，对问题进行解构分析
2、意图是要做什么，是用户问题中的主要目的
3、情形指的是实体在某一具体情况下所需要满足的条件或要求，对特定情形进行限制或要求的因素。
4、然后，基于问题目的按照“想要做什么，但遇到了什么困难或疑惑”句式进行转述。
5、再从转述中，根据用户的主要动作提取意图，根据遇到了什么困难或疑惑提取情形，根据转述内容，提取问题的主体类型
6、然后把提取出来的意图和情形以及主体类型放到意图集合、情形集合和主体类型集合中去匹配，注意，这里一定要非常匹配才行，如果能匹配到符合的字段，就放到返回的json中，如果不能准确匹配上，则返回空字符串。
7、如果匹配上了多个意图或者多个情形，返回的意图和情形用,分隔。
8、只返回结果，无需分析过程


#参考例子#
1、原始问题：有限责任公司变更住所，住所是租赁的需要提供什么材料？
分析：首先可以得出，主体类型为有限责任公司，意图为变更住所，可以匹配上意图集合中的“住所/经营场所”，情形为住所是租赁也可以匹配上情形集合中的“住所/经营场所”。然后分别将得出的主体类型、意图、情形和数据集定义中的内容进行匹配。
最终得出答案
{
    "主体类型":"内资有限责任公司及分公司",
    "意图": "住所/经营场所",
    "情形": "住所/经营场所"
}
#返回格式#
        {
            "主体类型":"",
            "意图": "",
            "情形": ""
        }