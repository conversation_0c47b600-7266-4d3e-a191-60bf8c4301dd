# 将用户的问题，拆分为寒喧、情绪、业务问题

# TODO 调一调提示词

from tools import get_prompt16, qwenMax,deepseek7b,qwen7b, qwenMax2
from ch19 import call_tx_hunyuan

# question = "内资企业怎么注销"
# question = "你好"
# question = "你们办理效率太低了"
# question = "你们办理效率太低了，内资企业怎么注销"   
question = "我改名字了，去哪里做变更呢？要带什么材料呢？"
test1 = "['申请企业名称','申请带“集团”字样的名称','申请带“研究院”、“研究所”、“研究中心”字样的名称','申请含“金融”字样的名称','申请不含行政区划的名称','名称要冠以“中国”、“中华”、“中央”、“全国”、“国家”等字眼','申请不含行业用语的名称','撤销已通过的名称申请','确认分公司名称的组织形式','修改未通过的名称','查询企业名称有无近似行业企业名称','明确禁止使用的涉金融企业的名称','申请使用XX修饰词作为行业用语','申请“医院“、“诊所”字样的名称','变更企业名称','字号名称变更','去除名称中的“前海”二字，不改变注册地址','申请冠省名的名称']";
test2 = "['变更经营范围','增加经营范围','减少经营范围','经营范围与经营方式变动备案','确认经营范围','想搞清楚怎么选经营范围','查询企业所经营的范围属于哪个行业代码','想要查询经营范围','核实经营范围在深圳前海深港现代服务业合作区产业准入目录中对应的属于哪一种','识别经营范围涉及哪些前置许可','识别经营范围涉及哪些后置许可']";
test3 = "['变更住所','同一行政区域住所迁移','企业迁入','企业迁出','办迁出申请','开具调入的接收函','跨区住所迁移','确认税务是否会同住所一起变更','迁入到深圳外的省份','办理一址多照变更','申请变更住所解除异常','核实地址是否属于前海片区','确认住所实地核查的时间','核实房产证号栏位应填写房产证上的哪个信息','开具迁移调档通知函','查询企业档案物流进度']";
test4= "['已经核名通过','行业用语选不到所需的选项','跨行业经营项目，没有其他行业类别适合选择','与集团公司字号重名','字号中有生僻字','去掉拟申请名称中的“责任”二字','有符合规定的名称']"
test5= "['经营范围选不到我想要的','之前都有的XX经营范围现在没有了','经营范围涉及前置许可','经营范围涉及后置许可','公司经营项目不涉及前置许可','从事煤炭进出口贸易和煤炭仓储运输','从事保安行业','开展彩盒包装业务','从事进出口贸易','公司经营范围与名称不匹配','经营范围满足前海准入目录','有符合规定的业务范围/经营范围','从事承包或接受委托经营管理外商投资企业','外国银行在中国设立分行']"
test6= "['属于网络经营场所','住所属于自有产权，但是没有房产证','住所属于自有产权，有房产证','住所还未签订租房合同','住所为租赁','租用的整层的办公地址，没有具体的房号或铺号','租用商业综合体店铺']"
args = {
    "messages" : [
        {
            "role": "user",
            "content": get_prompt16("ch18.txt",question,test1,test2)
        },
        {
            "role": "user",
            "content": "请结合上下文，根据问题从我新提供的意图集合和情形集合中，找出符合条件的意图和情形。请直接返回结果，无需分析过程。意图集合：" + test3 + "。情形集合：" + test4
        }
    ]
}
messages = [
        {
            "role": "user",
            "content": get_prompt16("ch18.txt",question,test1,test2)
        }
    ]
response = qwenMax(messages)

print(response)

#总控模块，在拆出业务问题时就拆出多个问题