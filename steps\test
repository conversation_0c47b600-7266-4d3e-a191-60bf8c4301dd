import json
content =
fileContent = getSlot(`fileContent`)
historyElements = getSlot(`historyElements`)
parsed_result = {}
try:
    content = content[content.index("{"):content.rindex("}")+1]
    parsed_result = json.loads(content)
except Exception as e:
    None
domain = '无法判断您的知识领域'
isComplete = '0'
elements = {}
missingElements = []
if '知识领域' in parsed_result:
    domain = parsed_result['知识领域']
if domain != '无法判断您的知识领域':
    # 这里就不校验domain的正确性了
    elements['知识领域'] = domain
    # 这里就不校验元素的缺失了
    for key, value in parsed_result.items():
        if key != '知识领域':
            if value != '需要询问' and value != '无需识别':
                # 这里也不校验元素名称的正确性了
                elements[key] = value
            elif value == '需要询问':
                for entity in fileContent["knowledgeDomains"]:
                    # 这里也不考虑同名的情况了
                    if entity["name"] == key:
                        if historyElements and domain in historyElements and key in historyElements[domain]:
                            elements[key] = historyElements[domain][key]
                        else:
                            missingElements.append({"name":key,"desc":entity['description']})
                        break
    if len(missingElements) == 0:
        isComplete = "1"

