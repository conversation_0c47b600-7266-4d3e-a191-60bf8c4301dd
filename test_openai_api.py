import requests
import os
from dotenv import load_dotenv

# 加载环境变量
load_dotenv()

# 打印环境变量值以进行调试
print("环境变量检查：")
print(f"API_BASE_URL: {os.getenv('OPENAI_API_BASE')}")
print(f"OPENAI_API_KEY: {os.getenv('OPENAI_API_KEY')}")
print(f"MODEL_NAME: {os.getenv('MODEL_NAME')}")

url = f"{os.getenv('OPENAI_API_BASE')}/chat/completions"
headers = {
    "Content-Type": "application/json",
    "Authorization": f"Bearer {os.getenv('OPENAI_API_KEY')}"
}
data = {
    "model": os.getenv('MODEL_NAME'),
    "messages": [
        {"role": "user", "content": "你好"}
    ]
}

response = requests.post(url, headers=headers, json=data)
print(response.status_code)
print(response.text) 