#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
批量繁体转简体脚本
适用于知识库数据转换入库
"""

import os
import json
import csv
from opencc import OpenCC
import sqlite3
from pathlib import Path


class BatchConverter:
    def __init__(self):
        # 初始化OpenCC转换器 (繁体转简体)
        self.cc = OpenCC('t2s')

    def convert_text(self, text):
        """转换单个文本"""
        if not isinstance(text, str):
            return text
        return self.cc.convert(text)

    def convert_txt_files(self, input_dir, output_dir):
        """批量转换txt文件"""
        input_path = Path(input_dir)
        output_path = Path(output_dir)
        output_path.mkdir(exist_ok=True)

        for txt_file in input_path.glob('*.txt'):
            print(f"正在转换: {txt_file.name}")

            with open(txt_file, 'r', encoding='utf-8') as f:
                content = f.read()

            # 转换内容
            simplified_content = self.convert_text(content)

            # 保存转换后的文件
            output_file = output_path / txt_file.name
            with open(output_file, 'w', encoding='utf-8') as f:
                f.write(simplified_content)

            print(f"已转换: {output_file}")

    def convert_csv_file(self, input_csv, output_csv, text_columns):
        """转换CSV文件中指定列的文本"""
        print(f"正在转换CSV: {input_csv}")

        with open(input_csv, 'r', encoding='utf-8') as infile:
            reader = csv.DictReader(infile)
            fieldnames = reader.fieldnames

            with open(output_csv, 'w', encoding='utf-8', newline='') as outfile:
                writer = csv.DictWriter(outfile, fieldnames=fieldnames)
                writer.writeheader()

                for row in reader:
                    # 转换指定列的内容
                    for col in text_columns:
                        if col in row and row[col]:
                            row[col] = self.convert_text(row[col])
                    writer.writerow(row)

        print(f"CSV转换完成: {output_csv}")

    def convert_json_file(self, input_json, output_json, text_fields):
        """转换JSON文件中指定字段的文本"""
        print(f"正在转换JSON: {input_json}")

        with open(input_json, 'r', encoding='utf-8') as f:
            data = json.load(f)

        def convert_json_recursive(obj, fields):
            if isinstance(obj, dict):
                for key, value in obj.items():
                    if key in fields and isinstance(value, str):
                        obj[key] = self.convert_text(value)
                    elif isinstance(value, (dict, list)):
                        convert_json_recursive(value, fields)
            elif isinstance(obj, list):
                for item in obj:
                    convert_json_recursive(item, fields)

        convert_json_recursive(data, text_fields)

        with open(output_json, 'w', encoding='utf-8') as f:
            json.dump(data, f, ensure_ascii=False, indent=2)

        print(f"JSON转换完成: {output_json}")

    def convert_and_insert_to_db(self, db_path, table_name, data_source, text_columns):
        """转换数据并直接插入数据库"""
        print(f"正在转换并插入数据库: {db_path}")

        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()

        if data_source.endswith('.csv'):
            with open(data_source, 'r', encoding='utf-8') as f:
                reader = csv.DictReader(f)

                for row in reader:
                    # 转换指定列
                    converted_row = {}
                    for key, value in row.items():
                        if key in text_columns and value:
                            converted_row[key] = self.convert_text(value)
                        else:
                            converted_row[key] = value

                    # 构建插入SQL
                    columns = ', '.join(converted_row.keys())
                    placeholders = ', '.join(['?' for _ in converted_row])
                    sql = f"INSERT INTO {table_name} ({columns}) VALUES ({placeholders})"

                    try:
                        cursor.execute(sql, list(converted_row.values()))
                    except Exception as e:
                        print(f"插入出错: {e}")
                        print(f"数据: {converted_row}")

        conn.commit()
        conn.close()
        print("数据库插入完成")


def main():
    converter = BatchConverter()

    # 使用示例1: 批量转换txt文件
    print("=== 批量转换TXT文件 ===")
    # converter.convert_txt_files('./traditional_texts/', './simplified_texts/')

    # 使用示例2: 转换CSV文件
    print("\n=== 转换CSV文件 ===")
    # converter.convert_csv_file(
    #     './knowledge_traditional.csv',
    #     './knowledge_simplified.csv',
    #     ['title', 'content', 'description']  # 需要转换的列名
    # )

    # 使用示例3: 转换JSON文件
    print("\n=== 转换JSON文件 ===")
    # converter.convert_json_file(
    #     './knowledge_traditional.json',
    #     './knowledge_simplified.json',
    #     ['title', 'content', 'summary']  # 需要转换的字段名
    # )

    # 使用示例4: 转换并直接入库
    print("\n=== 转换并入库 ===")
    # converter.convert_and_insert_to_db(
    #     './knowledge.db',
    #     'articles',
    #     './knowledge_traditional.csv',
    #     ['title', 'content']  # 需要转换的列名
    # )

    # 简单测试
    print("\n=== 转换测试 ===")
    test_texts = [
        "這是一個繁體中文的知識庫",
        "機器學習與人工智慧技術",
        "資料庫管理系統設計",
        "網際網路協議詳解"
    ]

    for text in test_texts:
        simplified = converter.convert_text(text)
        print(f"繁体: {text}")
        print(f"简体: {simplified}")
        print("-" * 50)


if __name__ == "__main__":
    # 安装依赖
    print("请先安装依赖: pip install opencc-python-reimplemented")
    print("如果处理数据库还需要其他库")
    print()
    sss='''
经初步验证，技术上可行，实现方式主要利用opencc库进行繁简转化，验证结论如下：
一、网站繁体知识爬取入库自动化转化为简体入知识库功能实现：
java对应的opencc依赖：
<dependency>
    <groupId>com.github.houbb</groupId>
    <artifactId>opencc4j</artifactId>
    <version>1.8.1<ersion>
</dependency>
二、前端繁简转换功能实现：
前端使用opencc-js库进行转化。
const OpenCC = require('opencc-js');
const converter = OpenCC.Converter({ from: 'hk', to: 'cn' });
const simplified = converter('繁體字轉換');
三、问答繁体回复：
用户输入问题原文传输到后台，脚本使用opencc-python-reimplemented库将用户问题转换为简体进行知识检索。
回复繁体回复，利用提示词实现，传入用户原始问题，然后加入对应提示词，即可实现模型直接繁体回复。

'''
    main()