# 将用户的问题，拆分为寒喧、情绪、业务问题
import random

from dashscope import Generation

from steps.tools import qwen72B,qwenMax2,qwen14B,qwen3,qwen7b

question = "我的公司需要变更法定代表人需要什么材料？"
messages = [
    {
        "role": "user",
        "content":"""## 任务
基于原问题和新的条件列表，更新问题中的查询条件，保持原问题的结构和表达方式不变。

## 输入
- 原问题：帮我拉一下所有 陆姿伽 经办、成交价格大于100的标段维度明细数据，要求展示标段编号、标段名称、经办人、采购立项审核通过时间
- 用户修改后的新条件列表：
```
[
    {"fieldnamecn": "经办人", "fieldvalue": "胡", "operation": "="},
    {"fieldnamecn": "成交价格", "fieldvalue": "390", "operation": "<"}
]
```

## 处理方式
1. 保持原问题的整体结构和表达方式
2. 根据conditionlist中的条件，替换原问题中对应的条件部分
3. 只替换变化的条件，其他部分保持原样

## 输出要求
直接输出更新后的完整问题，无需其他解释。"""
    }
]

response = qwen7b(messages)
print(response)
