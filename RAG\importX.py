
import base64
import io
import json
import os

import requests
from uuid import uuid4
import pandas as pd

from docx import Document
#
import openpyxl

import requests
import json
import sys
import os

from uuid import uuid4
from openai import OpenAI

root_path = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
sys.path.append(root_path)
from steps.tools import get_prompt,qwen7b,qwenMax,callKimi

from combine.ch5 import callKimi
from tqdm import tqdm
import datetime

import requests
from requests.models import HTTPBasicAuth


def file_split(file, file_name):
    """
    文件切分
    Parameters
    ----------
    file 文件url
    file_name 文件名称

    Returns
    -------

    """
    rag_split_url= "http://192.168.186.18:9002/linezhengshi_rag/split"
    headers = {
        'Content-Type': 'application/json'
    }
    data = {"split_method": "recursive",
            "chunk_size": 64,
            "file_name": file_name,
            "c_chunk_overlap": 0,
            "p_chunk_size": 256,
            "c_chunk_size": 128,
            "overlap": 0,
            "p_chunk_overlap": 0,
            "classname": "边聊边办",
            "attachguid": "001",
            "index_name": "nzyxzrgsv2_x2",
            "dbguid": str(uuid4()),
            "file": file,
            "separator": ["第[\d]{1,5}条", "\n\n"],
            }
    # print(data)
    response = requests.request("POST", rag_split_url, headers=headers, data=json.dumps(data))
    re = json.loads(response.text)
    return re

def createIndex(index_name):
    init_data = json.load(open("./RAGV2/schema.json",encoding="utf-8"))
    inteligentsearch_url = "http://192.168.186.65/inteligentsearch/rest"

    init_data["esdsid"] = 8
    init_data["category"]["categoryName"] = index_name
    init_data["category"]["categoryDescribe"] = index_name
    init_data["category"]["categoryNum"] = index_name

    init_data['category'] = json.dumps(init_data['category'])
        # {**(json.loads(init_data['category']) if isinstance(init_data['category'], str) else init_data['category']),
        #     'categoryNum': index_name}, ensure_ascii=False)
   
    build_url = inteligentsearch_url + "/esinteligentsearchmanageinit/addCategory"
    headers = {
        'Content-Type': 'application/json',
        "Authorization": "Bearer " + "token",
        "User-Agent": "python"
    }

    response = requests.post(url=build_url, json=init_data, headers=headers)
    print(response.text)

def embding(q_list):
    embed_url = "http://192.168.186.18:9081/linezhengshi_embedding/embed"
    data_embed = {"text": q_list}
    headers = {
        'Content-Type': 'application/json',
        "User-Agent": "python",
        "Authorization": "Bearer 4b1f2daed98265cc43aa7ebccfa89f0e",
    }
    response = requests.post(url=embed_url, data=json.dumps(data_embed), headers=headers)
    results = response.text
    return json.loads(results)["result"]

def insertData(ptype, index_name,purpose,subkey,content):
    es_url = "https://192.168.186.63:9200"
    id = str(uuid4())

    time_now = datetime.datetime.now().strftime('%Y-%m-%d')
    situation =""

    data = {
            "kworg_keyword":subkey,
            "kw_text":subkey,
            "kwvec_vector":embding([subkey])[0],

            "content_text":content,
            "contentvec_vector":embding([content])[0],   

            "ptype_keyword":ptype,
            "ptype_text":ptype,
            "ptypevec_vector":embding([ptype])[0],

            "purpose_keyword":purpose,
            "purpose_text":purpose,
            "purposevec_vector":embding([purpose])[0],

            "situation_text":situation,
            "situationvec_vector":embding([situation])[0],

            "id_keyword":id, "infodate_date": time_now, "syscategory_keyword": index_name
            }
    data = json.dumps(data)


    # result = self.data_inseart(self.es_url, index_name, data, id, self.username, self.password)
    headers = {'Content-Type': 'application/json'}
    insert_url = es_url + "/" + index_name + "/_doc" + "/"
    response = requests.put(insert_url + id, headers=headers, auth=HTTPBasicAuth("admin", "admin"),
                            verify=False, data=data.encode('utf-8'))
    try:
        re = json.loads(response.text)
        if "error" in re:
            error = re["error"]["type"]
            print(re)
            print(f"[*] insert应用中，导入数据失败，请检查传参，错误类型{error}")
        else:
            # pass
            result = re["result"]
            print(f"[*] 导入数据成功，主键：{id}，状态：{result}")
    except Exception as e:
        re = e
        print(f"[*] insert应用中， 导入数据出错，请检查es地址是否访问通，错误类型{e}，es接口返回{response.text}")
        


if __name__ == "__main__":
    # data_path = "./data/FAQ.docx"
    # with open(data_path, 'rb') as docx_file:
    #     stream = io.BytesIO(docx_file.read())
    #     file = base64.b64encode(stream.getvalue()).decode('utf-8')
    # file_name = os.path.basename(data_path)

    # # 切分调试
    # split_results = file_split(file, file_name)
    # chunk_list = split_results["result"]["chunk_list"]
    # for chunk in chunk_list:
    #     if len(chunk["text"]) > 800:
    #         print(chunk["text"])
    #         print("*" * 100)
    # print(len(chunk_list))

    
    index_name= "nzyxzrgsv2_x2"
    createIndex(index_name)

    # for item in chunk_list:
    #     content = item["text"]

    #     # result = insertData("", index_name,"","",content)

    folder_path = "./data/faqs"
    for file_name in os.listdir(folder_path):
        if file_name.endswith('.xlsx'):  # 筛选出 .xlsx 文件
            file_path = os.path.join(folder_path, file_name)
            print(f"正在处理文件: {file_name}")
            
            # 打开 Excel 文件
            excel_data = pd.read_excel(file_path,sheet_name=None,keep_default_na=False)
            
            # 遍历文件中的所有 sheet
            for sheet_name,sheet_data in excel_data.items():
                print(f"  Sheet 名称: {sheet_name}")
                
                # 遍历打印每一行
                for index, row in sheet_data.iterrows():
                    content = "问" + row.tolist()[0] + "答" + row.tolist()[1]
                    result = insertData("", index_name,"","",content)
                    print("%d,%d"%(index,len(sheet_data)))

            print("finish:" + file_path)
