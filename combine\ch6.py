## 按自己的RAG库，合并测试
import requests
import json
import os
import time
import sys
# 获取根目录路径
root_path = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
sys.path.append(root_path)
from steps.tools import  get_prompt,qwen7b,qwenMax,qwen14b


#详见steps/ch1
def getKeywords(centense):
    messages = [ {
        "role": "user",
        "content": get_prompt("getkeywords_template.txt",centense,[])
    }]

    response = qwen7b(messages)
    if "{" in response and "关键词" in response:
        response = json.loads(response)["关键词"]
    return response

#详见steps/ch9
def checkAB(centenst):
    messages = [ {
        "role": "user",
        "content": get_prompt("checkAB_template.txt",centenst,[])
    }]
    response = qwen14b(messages)
    return response

def queryES(keywords,indexName,queryfields,fields,vectorfield):
    
    inteligentsearch_url = "http://192.168.219.30:8080/epoint-blbb-web/rest/dynamicapi/callES"
    params = {
        "question": "aaa",
        "index_name": "nzyxzrgsv2_x2",
        "queryfield": "content",
        "vectorfield": "content",
        "selectfields": "content",
        "num": 10
    }
    headers = {
        'Content-Type': 'application/json',
        "Authorization": "Bearer " + "token",
        "User-Agent": "python"
    }
    response = requests.post(url=inteligentsearch_url,data=json.dumps(params), headers=headers)
    print(response.text)
    # re = json.loads(response.text)
    # ret =[]
    # for item in re["result"]:
    #     newitem ={}
    #     for field in fields:
    #         newitem[field] = item[field]
    #     ret.append(newitem)

    # return ret

def Rerankresults(data):
    url = "http://192.168.219.30:8080/epoint-blbb-web/rest/dynamicapi/reranker"
    headers = {
        'Content-Type': 'application/json',
        "User-Agent": "python",
        "Authorization": "Bearer 4b1f2daed98265cc43aa7ebccfa89f0e",
    }
    response = requests.post(url=url, data=json.dumps(data), headers=headers, verify=False)
    return json.loads(response.text)
    
#详见steps/ch12
def queryA(question,keywords):

    #分别用path和kw进行检索，合并记录
    result_retrieve1 = queryES(keywords,"nzyxzrgs27","path", ["path","content"],"path")
    result_retrieve2 = queryES(keywords,"nzyxzrgs27","kw", ["path","content"],"kw")

    lst = result_retrieve1 + result_retrieve2
    text_list = [pre["path"] for pre in lst]
    
    data_reranker = {"text": question, "compare_list": text_list}
    reranker_results = Rerankresults(data_reranker)
    
    #再进行rerank排序，取前10条
    results_reranker = reranker_results["result"]
    final_results = []
    for i, re_reranker in enumerate(results_reranker):
        content, score = re_reranker[0], re_reranker[1]
        item = lst[text_list.index(content)]
        final_results.append(item)
    final_results = final_results[:10]

    return final_results

#参考steps/ch1_b
def llmChoose(question,nodes):
    content = ""
    num = 0
    for item in nodes:
        num = num + 1
        content += "(" + str(num) + ")" + item["path"] +  "\n"

    # print(content)
    messages = [ {
        "role": "user",
        "content": get_prompt("routeChoise_template.txt",question,[]).replace("graphcontent",content)
        }, {
        "role": "assistant",
        "content": get_prompt("routeChoise_template.txt",question,[]).replace("graphcontent",content)
        }
    ]
    for i in range(5):
        try:
            response = qwen14b(messages)
            if "```json" in response:
                response = response.split("```json")[1].split("```")[0]
            print(response)
            dt = json.loads(response)
            if len( dt["回答"]) < 5 or dt["反问"] == "":  #少于5条，或者无反问时，直接返回结果
                txt =""
                path =""
                for idx in dt["回答"]:
                    path += nodes[int(idx)-1]["path"] + "\n"
                    txt += nodes[int(idx)-1]["path"] + ":" + nodes[int(idx)-1]["content"] + "\n"
                return path,txt
            else:
                return "反问",dt["反问"]
        except Exception as e:
            print(e)
            print(response)

    return response

#详见steps/ch11
def queryB(question,keywords):
    #分别用content和kw进行检索，合并记录
    result_retrieve2 = queryES(keywords,"nzyxzrgs19","kw", ["path","content"],"kw")
    result_retrieve1 = queryES(keywords,"nzyxzrgs19","content", ["path","content"],"content")

    lst = result_retrieve1 + result_retrieve2
    
    text_list = [pre["content"] for pre in lst]
    data_reranker = {"text": question, "compare_list": text_list}
    reranker_results = Rerankresults(data_reranker)
    results_reranker = reranker_results["result"]

    final_results = []
    for i, re_reranker in enumerate(results_reranker):
        content, score = re_reranker[0], re_reranker[1]
        item = lst[text_list.index(content)]
        final_results.append(item)

    num = 0
    content = ""
    for item in final_results:
        num += 1
        if num >3:
            break
        if "content" in item:
            content += item["path"] + "\n" + item["content"] + "\n\n"
    return content



if __name__ == '__main__':
    # ts = time.time()
    # # question = "与朋友合伙开的公司，还未经营，我是法人，现在找工作不能面试，想要申请法人变更"
    # question = "内资企业怎么注销"
    # # question = "我司注册地址为深圳市福田区福田街道，为外商独资企业，现已经办结一笔股权出质登记，后续拟办理该笔股权出质登记的注销，并新设一笔股权出质登记。请问：深圳市的预约系统中，是否在注销登记完成的当天或之前即可办理新设登记的预约？"
    # keywords = getKeywords(question)

    # print(keywords)

    # ab = checkAB(question)
    # print(ab)

    # content =""
    # if "A" in ab:
    #     nodelst = queryA(question,keywords)
    #     print("\r\n".join([item["path"] for item in nodelst])) #debug
    #     path, content = llmChoose(question,nodelst)
    #     print("")
    #     if path == "反问":
    #         print(content)
    #     else:
    #         print("以下为挑选的路径：")
    #         print(path)
    # else: #B类
    #     content =queryB(question,keywords)
    #     print(content)


    # print("")
    # print(len(content))
    # print("用时：" + str((time.time() - ts)))

    queryES("","","","","")


