
##根据ES检索结果，批量刷记录
import openpyxl

import requests
import json
import sys
import os

root_path = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
sys.path.append(root_path)
from steps.tools import get_prompt,qwen7b,qwenMax

# from ch11 import queryB
# from ch12 import queryA
from combine.ch5 import callKimi
from tqdm import tqdm


def getKeywords(centense):
    messages = [ {
        "role": "user",
        "content": get_prompt("getkeywords_template.txt",centense,[])
    }]
    for i in range(3):
        try:
            response = qwen7b(messages)

            if "{" in response and "关键词" in response:
                response = json.loads(response)["关键词"]

            return response
        except Exception as e:
            continue

def get_results(data):
    reranker_url = "http://192.168.186.112:8080/epoint-gateway/linezhengshi_reranker/predict"
    headers = {
        'Content-Type': 'application/json',
        "User-Agent": "python",
        "Authorization": "Bearer 4b1f2daed98265cc43aa7ebccfa89f0e",
    }
    response = requests.post(url=reranker_url, data=json.dumps(data), headers=headers, verify=False)

    results = response.text

    return json.loads(results)

def queryAB(question,keywords):
    url = "http://192.168.219.30:8080/epoint-blbb-web/rest/dynamicapi/queryA"
    headers = {
        'Content-Type': 'application/json',
        "User-Agent": "python",
        "Authorization": "Bearer 4b1f2daed98265cc43aa7ebccfa89f0e",
    }
    data ={"question":question,"keywords":keywords,"top":"10"}

    response = requests.post(url=url, data=json.dumps(data), headers=headers, verify=False)
    dt = json.loads(response.text)

    content = []
    for item in dt["content"]:
        content.append(item["path"] + "\r\n" + item["content"])
    
    url = "http://192.168.219.30:8080/epoint-blbb-web/rest/dynamicapi/queryB"
    headers = {
        'Content-Type': 'application/json',
        "User-Agent": "python",
        "Authorization": "Bearer 4b1f2daed98265cc43aa7ebccfa89f0e",
    }
    data ={"question":question,"keywords":keywords,"top":"10"}

    response = requests.post(url=url, data=json.dumps(data), headers=headers, verify=False)
    dt = json.loads(response.text)
    for item in dt["lst"]:
        content.append(item)
    content.append("第二章、登记备案规则-第十八节、注册资本-通用规则 第110条，除法律、行政法规或者国务院决定另有规定外，市场主体的注册资本或者出资额实行认缴登记制，以人民币表示。--依据《中华人民共和国市场主体登记管理条例》第十三条")
    
    data_reranker = {"text": question, "compare_list": content}
    reranker_results = get_results(data_reranker)

    content = []
    for item in reranker_results["result"][:10]:
        content.append(item[0][:50].replace("\n"," "))
    return content


centence ="自然人独资企业最低注册资金的是多少？有哪些缴纳方式？通用规则"

keywords = getKeywords(centence)
keywords = keywords
print(keywords)


ret = queryAB(centence,keywords)

for index, item in enumerate(ret, start=1):
    print(f"{index}. {item}")
