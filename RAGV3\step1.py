
import sys
import os

root_path = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
sys.path.append(root_path)
from steps.tools import get_rag3_prompt, qwenMax2

#大模型涉敏判断
question = "毕业后进入深圳一家外企工作，户籍档案则调入“深圳人才集团”，现在我没有工作，在深圳也没有房产，但我在深圳罗湖区续保了“灵活就业社保”，要求我迁出户籍，请问我的户籍可以迁往何处"
messages = [
        {
            "role": "user",
            "content": get_rag3_prompt("ch1.txt",question,'','')
        }
    ]
response = qwenMax2(messages)

print(response)


