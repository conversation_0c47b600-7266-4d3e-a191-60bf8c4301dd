import datetime
import sys
import os
import time

# 添加项目根目录到 Python 路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from steps.tools import qwen3
from dashscope import Generation
import random


def qwen3_stream(messages):
    """Qwen3流式输出版本"""
    model = "qwen3-32b"
    api_key = "sk-e6caa51d2a8d45818bc482e3e71e8057"
    seed = random.randint(1, 10000)
    response = Generation.call(
        model,
        messages=messages,
        api_key=api_key,
        seed=seed,
        result_format='message',
        temperature=0.,
        enable_thinking=True,
        stream=True,  # 启用流式输出
        stop='Observation'
    )

    # 处理流式响应
    for chunk in response:
        if hasattr(chunk, 'output') and hasattr(chunk.output, 'choices'):
            if chunk.output.choices and len(chunk.output.choices) > 0:
                choice = chunk.output.choices[0]
                if hasattr(choice, 'message') and hasattr(choice.message, 'content'):
                    content = choice.message.content
                    if content:
                        yield content
                elif hasattr(choice, 'delta') and hasattr(choice.delta, 'content'):
                    content = choice.delta.content
                    if content:
                        yield content


prompt = """
## 角色职责
作为政府工作人员，只能通过我提供的`知识`进行回答，严禁使用预训练的知识。

**首要判断原则：**
当`知识`内容无法明确回答`用户问题`的时候，直接回复"对不起，我无法回答你的问题"，不再进行任何其他处理或结构化回答。

**当知识内容可以回答用户问题时：**
你的回答必须**逻辑清晰、层次分明、重点突出、便于用户快速理解和行动**。避免信息堆砌、结构混乱或遗漏关键要素。

## 用户问题
我在加拿大读书，需要工作签证，邮寄申请刑事记录证明书要选什么用途？


## 知识
```
<事项办事指南>
[1].刑事纪录证明书（行为纸）－ 邮寄申请,一、服务简介：
无
二、查询途径：
无
三、如何办理：
办理手续及所需档

如申请人身处澳门以外地方，可提出邮寄申请。

申请步骤：

登入身份证明局"邮寄申请服务"系统，取得申请编号及打印凭条；
将"申请凭条的第二部份"连同"申请所需档"一并寄回身份证明局 （邮寄地址：澳门邮箱1089 号）；
身份证明局收到申请档后，会透过"邮寄申请服务"系统通知申请进度，申请人应适时登入查看；
当申请状态为"请付费"时，申请人可缴付费用及完成申请。
申请所需档：

1. 申请信函正本。信函中应写上：姓名、父母姓名、出生日期及地点、申请证明书的用途（请参阅用途列表）、联络方法及说明所需的申请类型（加快申请或普通申请），申请人须在信函上签署，并在签名旁印上申请人右手食指指模。

2. 申请人的澳门特区居民身份证副本；

3. 如申请人不持有澳门特区居民身份证，须递交：

由申请人现居地有权限的机关（如警察局或执法机关等）所印取的清晰十指指模正本。指模表上应载有该权限机关的名称及印章，表格可在本局网页下载；亦可直接使用当地警方的专用表格；（供申请刑事纪录证明书之用的指模卡空白表格）；
如与澳门地区有联系的证明，例如：居留证、外地僱员身份认别证（非本地劳工身份卡）、学校证明或申请人在澳申请定居的证明（如治安警察局发出的申请居留许可的通知书或招商投资促进局发出的须提交澳门刑事纪录证明书之档等）；
申请人有效旅行证件的鉴证本；
4. 如申请人不持有澳门居民身份证而需在证明书上登载父母姓名，请一并递交有关证明档的影印本，如出生证明，曾递交者无须再递交。
四、服务办理地点及时间：
身份证明局

总办事处
地址：澳门南湾大马路804号中华广场一楼
办公时间：周一至周五，上午9时至下午6时（周六、日及政府假期休息）
政府综合服务大楼
地址：澳门黑沙环新街52号政府综合服务大楼二楼R区
办公时间：周一至周五，上午9时至下午6时（周六、日及政府假期休息）
离岛政府综合服务中心
地址﹕凼仔哥英布拉街225号三楼离岛政府综合服务中心D区
办公时间：周一至周五，上午9时至下午6时（周六、日及政府假期休息）
五、费用：
费用：发出费用 %2B 邮寄费用 （身份证明局将透过"邮寄申请服务"系统通知申请人收费总额）

发出费用：
普通申请：澳门元50元。
加快申请：澳门元150元。
邮寄费用 : 金额视乎邮寄地区及重量而定。
付款方式：

网上缴费：透过"邮寄申请服务"系统以信用卡支付。
由受托人代付费用：申请人可在申请信函中注明由在澳受托人亲临本局以现金或电子方式支付有关费用。
附注:

邮寄申请只接受上述两种方式缴付费用。
如遇上系统故障或其他特别情况而引致申请人缴付了不应由本地区政府收取的款项，请尽快联络身份证明局，身份证明局将退回相关款项。
当网上交易完成后，如申请人欲取消申请及退回款项，请以书面方式向身份证明局提出，以便处理退款手续。
六、审批所需时间：
普通申请：5个工作日。（服务承诺）
加快申请：2个工作日。（服务承诺）
附注：自收齐申请所需档之翌日起计，不包括邮寄所需时间。
七、相关规范或要求
无
八、查询进度及领取服务结果：
查询进度﹕

申请人可透过以下方式查询：

网上查询：登入身份证明局网页的"邮寄申请服务"系统；
致电本局查询（电话：(853) 28370777、(853) 28370888）；
电邮查询（<EMAIL>）；
亲临查询。


领取服务结果的方式：

由身份证明局寄出。
亲临领取（于申请时提出）。
委托他人代领（于申请时提出）。
委托在澳受托人亲临身份证明局领取的委托书；
受托人在代领证明书时，须出示其有效的身份证明文件正本及申请人的身份证明文件影印本，以供核对。
九、备注/申请须知：
《刑事纪录证明书》有效期：自发出日起计 90日内有效。



<法律法规>
[2].第27/96/M号法令,第二章第十四条,一、刑事纪录证明书之申请书，应呈交予身份证明局。
二、如利害关系人不在澳门特别行政区，得要求邮寄申请表。
三、申请人在适当填写申请表，并附同其本人之身份证明文件影印本，以及写上证明书之回邮地址后，应将申请书以挂号方式寄回身份证明局。



<办事渠道>
[3].部门：
身份证明局

刑事纪录证明书（行为纸）－ 由本人申请：
透过"一户通"手机应用程序办理：
需下载"一户通"手机应用程序
请参阅身份证明局网上服务

刑事纪录证明书（行为纸）－ 他人代为办理：
无

刑事纪录证明书（行为纸）－ 邮寄申请：
无

未成年人特别纪录证明书 － 亲临或他人代为办理：
无

未成年人特别纪录证明书 － 邮寄申请：
无

[4].部门：
身份证明局

总办事处
地址：澳门南湾大马路804号中华广场一楼
办公时间：周一至周五，上午9时至下午6时（周六、日及政府假期休息）
政府综合服务大楼
地址：澳门黑沙环新街52号政府综合服务大楼二楼R区
办公时间：周一至周五，上午9时至下午6时（周六、日及政府假期休息）
离岛政府综合服务中心
地址﹕凼仔哥英布拉街225号三楼离岛政府综合服务中心D区
办公时间：周一至周五，上午9时至下午6时（周六、日及政府假期休息）



<实操业务规则>
[5].邮寄申请时，可以委托在澳亲友代交费用，申请人可以委托在澳门的亲友代为支付有关费用，只需在寄予身份证明局的申请信中提供在澳门的亲友的姓名及联络电话，身份证明局会在申请材料齐备后，通知其前往身份证明局为申请人缴交费用。

```

## 返回要求
一、地域限定规则​​
所有政策解释限定深圳范围，禁止提及/对比其他地区。遇外地咨询自动适用澳门规则。
二、实体信息全量输出​​
1、链接/地址需原文呈现

​​四、交互响应规范​​
1、可行性问题直接回复可行性结论
2、操作类问题分步骤说明
3、禁止复述用户问题
五、内容呈现标准​​
1、时间表述统一为 年月日格式，类似 2004年4月2日
2、禁止出现"第X章第X条"类引用
3、重要字段加粗，分段添加小标题
4、结尾禁止添加知识来源说明
5、**回答的所有内容用中文繁体字答复**

六、知识序号标记规范​​
1、 [1],[2],[3]...是知识序号，问题答复时需要标记知识的序号和知识类型。
2、标记格式：单个标记为^[1|知识类型]^，多个标记为^[1|知识类型]^^[2|知识类型]^。举例：单个标记为  ^[1|法律法规知识]^，多个标记为 ^[1|法律法规知识]^^[2|实操业务规则知识]^
3、标记位置：必须置于句尾标点符号前

七、样式要求
请严格遵循以下通用原则来组织你的回答结构：
"()"中的内容要求特别关注
**如果知识中同时存在事项办事指南、法律法规条目，优先以事项办事指南回答用户的问题，最后是法律法规条目**，例如：材料、时限、办理条件等。
思考：是否已经完全按照优先事项办事指南、其次法律法规条目的优先级顺序回答用户问题？
1.  **结论(金字塔顶端)：**
    *   **当知识内容可以明确回答用户问题时：第一句话/第一段必须清晰、直接地给出用户问题的核心答案或最关键的行动建议。** 这是用户最关心的信息，务必开门见山，避免任何冗长铺垫。加粗回答。
    *   **当知识内容无法明确回答用户问题时：直接回复"对不起，我无法回答你的问题"，不再继续后续的结构化回答。**
    *   **核心结论中如果提及办理时限，必须说明是法定办结时限还是承诺办结时限。
    *   *思考：知识内容是否能够明确回答用户问题？如果可以，用户问的核心是什么？最需要知道的结果或下一步是什么？*
2.  **逻辑分组 (MECE原则)：**
    *   将回答中涉及的所有信息点，按照其**内在逻辑关系和属性**进行清晰分组。确保每个组别内的信息是**相互独立、完全穷尽** 的，但不要正文中直接输出逻辑分组这四个字及方法名称。
    *   常见的逻辑分组维度包括（但不限于）：
        *   **"是什么/为什么/怎么做"** (What/Why/How)
        *   **"依据/条件/材料/流程/时限/费用"** (常见政务要素)
        *   **"核心要求/支持信息/行动步骤"**
        *   **"适用情形/不适用情形"**
        *   **"优势/限制"** (如果涉及比较或选择)
    *   *思考：这些信息点可以自然地分成哪几个互不重叠、又覆盖所有必要内容的大类？*
3.  **层级递进 (重要性/逻辑顺序)：**
    *   在确定了逻辑分组后，**为这些分组定义一个合理的呈现顺序**。顺序应服务于用户的理解和行动效率。
    *   优先考虑以下顺序：
        *   **重要性顺序：** 用户最关心、最关键的信息（如核心结论、行动步骤）优先呈现。
        *   **逻辑顺序：**
            *   **认知顺序：** 先了解"是什么"（定义/政策），再了解"为什么"（依据/背景），最后知道"怎么做"（流程/材料）。适用于解释性内容。
            *   **时间顺序/流程顺序：** 严格按照事件发生的先后步骤描述（如办事流程）。适用于操作指南。
            *   **结构顺序：** 将整体拆解为部分（如不同办理渠道、不同材料类型）。
        *   **空间顺序：** 较少用，但描述地理位置相关时适用。
    *   *思考：用户了解这些信息的自然思维路径是怎样的？他们需要先知道什么才能理解下一步？*
4.  **模块化与清晰标识：**
    *   使用**清晰、一致的标题或标识**（如 `## 标题` 或 **加粗关键词**）来标记每个逻辑分组，并且**需要标记序号**，例如： 一、二、三...。这为用户提供视觉锚点，快速定位信息。
    *   每个逻辑分组内部的信息点，**如果条目较多（≥3条）或需要强调清晰度，必须使用分点列表（• 或 1. 2. 3.）** 呈现。避免长段落堆砌。
    *   *思考：如何让用户一眼就能找到"办理流程"在哪？"所需材料"在哪？*
5.  **动态模块 (按需包含)：**
    *   以下关键信息模块**按需包含**（如果该问题适用）：
        *   **核心结论/关键行动** (永远放在最前)
        *   **政策/法规依据** (说明答案的权威来源)
        *   **办理条件/适用情形** (明确边界)
        *   **所需材料清单** (明确要求)：材料清单逐项分行列示，保留原始适用条件说明
        *   **办理流程/操作步骤** (指导行动)：线上办理流程、线下办理流程、线上办理地点、线下办理地点必须**全量展示**所有区域信息（**禁止出现"详见知识库"表述**），若涉及线上办理地址，按照线上和线下分类，先输出线上地址，再输出线下地址
        *   **办理时限** (设定预期)
        *   **费用说明** (透明告知)
        *   **官方咨询渠道** (提供求助路径)
        *   **温馨提示** (提供用户需要额外注意的事项，例如：亲属额外可以享受的优待？需要额外注意的事项)
    *   **判断依据：** 根据用户问题的具体内容和性质，**动态决定哪些模块是必要的**。不存在的模块（如问题不涉及费用）则省略，避免冗余。模块的**出现顺序必须符合第3条（层级递进）的原则****模块之间不要出现相同内容重复输出的情况**。
    *   *思考：用户这个问题涉及到上面哪些模块？哪个模块应该紧接着核心结论出现？哪个模块是后续步骤的基础？是否一定需要注意事项？如果需要注意事项，注意事项放在最后了吗？*
6.  **逻辑连接与过渡：**
    *   在模块之间或关键信息点之间，使用**简洁自然的过渡语或逻辑连接词**，表明它们之间的关系（如：因此、接下来、同时、需要注意的是、具体来说、例如等），增强行文的连贯性和可读性。避免生硬跳跃。
基于用户问题扩展、补充的模块，需要有承上启下的过渡话术，例如：用户询问"社保断缴会影响养老金吗？"，如果需要告知用户"如何断缴、补缴、续缴等问题"，需要有承上启下的过渡话术
## 写作规范 (通用政务要求)
*   **语言：** 规范、准确、简洁、庄重。使用官方标准术语。避免口语化、模糊词汇、主观评价。
*   **客观性：** 仅陈述事实、政策和规定流程。
*   **人称：** 推荐使用中性表述（如"需提交..."、"申请人应...")或第三人称。
*   **完整性：** 覆盖用户问题所需的所有关键信息点，避免遗漏导致用户需要二次询问。
*   **简洁性：** 在保证准确和完整的前提下，力求简洁。删除无关信息。
## 最终输出要求
1.  **严格应用结构化构建原则：** 确保回答体现"结论先行"、"逻辑分组"、"层级递进"、"模块化"、"动态模块"、"逻辑连接"。
2.  **优先用户理解：** 结构服务于用户快速获取信息、理解逻辑、采取行动。
3.  **保持一致性：** 相同类型的问题，其回答的核心结构逻辑应保持一致，提升用户体验的可预测性。
4、**额外注意：**注意事项章节必须在最后段落。
## 禁止
*   避免"总分总"结构中不必要的"总"（开头结论是必要的"总"，结尾通常不需要总结，除非是复杂决策建议）。政务回答应直接高效。
*   避免信息点散乱分布、缺乏分组、缺失标题序号和层次。
*   避免长段落、不分点、无标识的信息堆砌。
*   相同的内容不允许重复输出；
*   答案中禁止出现通用原则的名称，例如：结论、逻辑分组、层级递进、模块化与清晰标识、动态模块、逻辑连接与过渡。
*   禁止输出注意事项；
"""

messages=[{
    "role":"user",
    "content":prompt
}]


try:
    for chunk in qwen3_stream(messages):
        print(chunk, end='', flush=True)
        time.sleep(0.01)
    print("\n" + "=" * 50)
    print("回答完成")
except Exception as e:
    print(f"发生错误: {str(e)}")

# print("\n\nAI回答（非流式输出）:")
# result = qwen3(messages)
# print(result)
