import pandas as pd
import requests

from tools import qwenMax

# 将用户的问题，拆分为寒喧、情绪、业务问题
# TODO 调一调提示词

# question = "内资企业怎么注销"
# question = "你好"
# question = "你们办理效率太低了"
# question = "你们办理效率太低了，内资企业怎么注销"
# 读取 Excel 文件
file_path = r'C:\Users\<USER>\Desktop\check\chunk_child_3.xlsx'  # 替换为你的 Excel 文件路径
df = pd.read_excel(file_path)

# 循环遍历 DataFrame 的每一行
for index, row in df.iterrows():
    entity = row['entity_type_name']
    intent = row['intent_name']
    condition = row['condition_name']
    queryTxt = '我是一个*主体类型*，想要*意图*，但是*情形*，请问在这样的情况下，给我尽可能详细的指导信息，以及在深圳市如何申请办理的信息，以便我了解并应对可能的问题。'
    # 检查三个字段是否都有内容
    if pd.notna(entity):
        queryTxt = queryTxt.replace('*主体类型*', entity)
    if pd.notna(intent):
        queryTxt = queryTxt.replace('*意图*', intent)
    if pd.notna(condition):
        queryTxt = queryTxt.replace('*情形*', condition)

    messages = [
        {
            "role": "system",
            "content": queryTxt
        }
    ]

    try:
        # 获取响应数据
        df.at[index, 'H'] = qwenMax(messages)
        df.to_excel(file_path, index=False)
        print(index)
    except requests.RequestException as e:
        print(f"请求发生错误: {e}，行索引: {index}")





# 总控模块，在拆出业务问题时就拆出多个问题
