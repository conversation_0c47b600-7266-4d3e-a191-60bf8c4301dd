import base64
import json
import re

import pandas as pd
import requests
import os
from tools import get_prompt,qwenMax2,qwenMax,deepseek7b
from ch19 import call_tx_hunyuan
#【深圳边聊边办】领域-主体类型-意图--情形提取测试

#意图情形识别
def getIntensionAndCondition(question):
    domain=""
    #获取领域
    prompt = get_prompt("questionsplit_ly.txt",question,"","","")
    messages = [
        {
            "role": "user",
            "content": prompt
        }
    ]
    firstcatagoryobj = qwenMax2(messages)
    content_data = json.loads(firstcatagoryobj)
    if content_data is not None:
        domain = content_data.get('领域')

    resultobj={}
    resultobj["领域"] = domain
    return resultobj

def extract_json_from_string(text):
    # 使用正则表达式匹配被```json包裹的JSON数据[[7]]
    pattern = r'```json\n(.*?)\n```'
    matches = re.findall(pattern, text, re.DOTALL)

    results = []
    for match in matches:
        try:
            # 解析JSON字符串[[2, 6, 15]]
            json_data = json.loads(match.strip())
            results.append(json_data)
        except json.JSONDecodeError:
            continue

    return results[0] if results else None
def process_excel():
    parent_dir = os.path.dirname(os.getcwd())
    file_path = os.path.join(parent_dir, 'data', 'data5.xlsx')
    # 读取Excel文件
    df = pd.read_excel(file_path, engine='openpyxl',keep_default_na=False)
    # 遍历A列，拼接message
    for index, row in df.iterrows():
        # 获取A列的值作为问题
        question = row['faq_title']
        if(question):
            result = getIntensionAndCondition(question)
            print(result)
            df.at[index, '二维领域'] = result['领域']

        # 保存修改后的Excel文件
        df.to_excel(file_path, index=False, engine='openpyxl')

if __name__ == '__main__':
    process_excel()
