import csv
import json
import uuid

entitys = {
    "entities":[]
}
tempEntity = []
factory3 = {}
with open('市场主体登记指南图谱-内资有限.csv', mode='r', newline='', encoding='utf-8') as file:
    reader = csv.reader(file)
    # 跳过标题行
    next(reader)
    for row in reader:
        if row:
            if row[0]:
                id2 = str(uuid.uuid4()).replace("-", "")[:16]
                entity = {
                    "name": row[0],
                    "id": str(uuid.uuid4()).replace("-", "")[:16],
                    "relations": [
                        {
                            "target": id2
                        }
                    ]
                }
                entitys["entities"].append(entity)
                entity2 = {
                    "name": row[5],
                    "id": id2,
                    "description": row[6],
                    "relations": [
                    ]
                }
                entitys["entities"].append(entity2)
            elif row[5]:
                id2 = str(uuid.uuid4()).replace("-", "")[:16]
                entity["relations"].append({
                    "target": id2
                })
                entity2 = {
                    "name": row[5],
                    "id": id2,
                    "description": row[6],
                    "relations": [
                    ]
                }
                entitys["entities"].append(entity2)
            elif row[10]:
                if row[10] in factory3:
                    entity3 = factory3[row[10]]
                    id3 = entity3["id"]
                else:
                    id3 = str(uuid.uuid4()).replace("-", "")[:16]
                    entity3 = {
                        "name": row[10],
                        "description": row[11],
                        "id": id3,
                    }
                    factory3[row[10]] = entity3
                    # entitys["entities"].append(entity3)
                    tempEntity.append(entity3)
                entity2["relations"].append({
                    "target": id3
                })
entitys["entities"].extend(tempEntity)
print(json.dumps(entitys, ensure_ascii=False))

