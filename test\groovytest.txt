import groovy.json.JsonSlurper
import groovy.json.JsonOutput
import groovy.json.JsonBuilder
import java.util.regex.Pattern

def main(metrics) {
	// 当前时间
	def currentDate = new Date().format('yyyy-MM-dd')
	// 解析指标结果
	def configErrorMsg = ""
	if(!metrics){
		configErrorMsg = "转换指标失败"
	}

	// 先转换为JSON字符串
	def jsonString = JsonOutput.prettyPrint(JsonOutput.toJson(metrics))

	// 将Unicode编码转换回中文字符
	def unescapedJson = unescapeUnicode(jsonString)

	return [
		errormsg : configErrorMsg,
		metrics : unescapedJson,
		datetime : currentDate
	]
}

// 将Unicode编码转换为中文字符的方法
def unescapeUnicode(String str) {
	def pattern = Pattern.compile("\\\\u([0-9a-fA-F]{4})")
	def matcher = pattern.matcher(str)
	def result = new StringBuffer()

	while (matcher.find()) {
		def unicode = matcher.group(1)
		def ch = (char) Integer.parseInt(unicode, 16)
		matcher.appendReplacement(result, String.valueOf(ch))
	}
	matcher.appendTail(result)

	return result.toString()
}