# -*- coding: utf-8 -*-
"""
@Des     : 检索图谱中的数据，插入到库， 作为A类问题
检索word中的数据，拆分，插入库库，作为B类问题
"""
import datetime
import json
import math
import re
import time
from uuid import uuid4
from docx import Document
import sys
import os

import requests
from requests.models import HTTPBasicAuth

root_path = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
sys.path.append(root_path)
from steps.tools import get_prompt,qwen7b,qwenMax
from importWord import getKeywords,createIndex,embding,insertData

class Restful(object):

    def __init__(self, url):

        self.url = url

    def get_results(self, data):
        headers = {
            'Content-Type': 'application/json',
            "User-Agent": "python",
            "Authorization": "Bearer 4b1f2daed98265cc43aa7ebccfa89f0e",
        }
        response = requests.post(url=self.url, data=json.dumps(data), headers=headers)

        results = response.text

        return json.loads(results)


class Question2ES(object):

    def __init__(self, args):

        self.args = args
        self.init_data = self.args["init_data"]
        self.esds_id = self.args["esds_id"]
        self.inteligentsearch_url = self.args["inteligentsearch_url"]
        self.embed_url = self.args["embed_url"]
        self.dbguid = self.args.get("dbguid", None)
        self.es_url = self.args["es_url"]
        self.username = self.args["username"]
        self.password = self.args["password"]
    @staticmethod
    def build_index(init_data, index_name, esds_id, inteligentsearch_url):
        """
        检查并建立索引索引
        Parameters
        ----------
        index_name:索引名称
        init_data:初始化数据

        Returns
        -------

        """

        init_data['category'] = json.dumps(
            {**(json.loads(init_data['category']) if isinstance(init_data['category'], str) else init_data['category']),
             'categoryNum': index_name}, ensure_ascii=False)
        if esds_id is not None:
            try:
                esdsid = int(esds_id)
                init_data["esdsid"] = esdsid
            except ValueError:
                raise ValueError(f"Invalid input esdsid: '{esds_id}' is not an integer.")
        build_url = inteligentsearch_url + "/esinteligentsearchmanageinit/addCategory"
        headers = {
            'Content-Type': 'application/json',
            "Authorization": "Bearer " + "token",
            "User-Agent": "python"
        }

        response = requests.post(url=build_url, json=init_data, headers=headers)

        re = json.loads(response.text)
        code = re["code"]
        msg = re["msg"]
        if str(code) == "200":
            return f"[*] 索引{index_name}建立成功！"
        if str(code) == "500" and msg == "目标数据源已存在同名元数据":
            return f"[*] 索引{index_name} 已存在，请忽重复建立"
        else:
            raise ValueError(f"[*] 建索引错误，请检查es是否访问通，es错误信息{msg}!")



    def data_inseart(self, es_url, index_name, data, id, username, password):
        """

        Parameters
        ----------
        index_name
        data
        id

        Returns
        -------

        """

        headers = {'Content-Type': 'application/json'}
        insert_url = es_url + "/" + index_name + "/_doc" + "/"
        response = requests.put(insert_url + id, headers=headers, auth=HTTPBasicAuth(username, password),
                                verify=False, data=data.encode('utf-8'))
        try:
            re = json.loads(response.text)
            if "error" in re:
                error = re["error"]["type"]
                print(re)
                print(f"[*] insert应用中，导入数据失败，请检查传参，错误类型{error}")
            else:
                # pass
                result = re["result"]
                print(f"[*] 导入数据成功，主键：{id}，状态：{result}")
        except Exception as e:
            re = e
            print(f"[*] insert应用中， 导入数据出错，请检查es地址是否访问通，错误类型{e}，es接口返回{response.text}")
        return re

    @staticmethod
    def inference_embed(embed_url, q_list):
        data_embed = {"text": q_list}
        encode_engine = Restful(embed_url)
        embed_results = encode_engine.get_results(data_embed)
        vectors = embed_results["result"]
        return vectors


    def __call__(self, question, index_name,path,graphid,keywords):
        """
        问题转向量
        :param question: 问题
        :param index_name: 索引名称
        :return:
        """
        # 参数定义

        if not self.dbguid:
            self.dbguid = str(uuid4())
        id = str(uuid4())
        # step1 建立索引
        self.build_index(self.init_data, index_name, self.esds_id, self.inteligentsearch_url)

        # step2 导入数据准备
        embed_results = self.inference_embed(self.embed_url, [question])[0]
        if not keywords:
            keywords =""
        keywords_results = self.inference_embed(self.embed_url, [keywords])[0]
        time_now = datetime.datetime.now().strftime('%Y-%m-%d')

        if path=="":
            data = {"dbguid_keyword":self.dbguid, "contentvec_vector":embed_results,"contentinfo_keyword":question, "content2_text":question,"keywords_text":keywords,
                     "id_keyword":id, "graphid_keyword":graphid,"infodate_date": time_now, "syscategory_keyword": index_name}
            data = json.dumps(data)
        else:
            path_results = self.inference_embed(self.embed_url, [path])[0]
            data = {"dbguid_keyword":self.dbguid, 
                    "kworg_keyword":keywords,
                    "kw_text":keywords,
                    "kwvec_vector":keywords_results,

                    "pathorg_keyword":path,
                    "path_text":path,
                    "pathvec_vector":path_results,

                    "content_text":question,
                    "contentvec_vector":embed_results,                    
                     "id_keyword":id, "graphid_keyword":graphid,"infodate_date": time_now, "syscategory_keyword": index_name}
            data = json.dumps(data)

            # data = f'{{"dbguid_keyword":"{self.dbguid}", "contentvec_vector":{embed_results},"contentinfo_keyword":"{question}",' \
            #        f' "content2_text":"{path}","content1_text":"{question}","keywords_text":"{keywords}", "id_keyword":"{id}", "graphid_keyword":"{graphid}","infodate_date": "{time_now}", "syscategory_keyword": "{index_name}"}}\n'

        result = self.data_inseart(self.es_url, index_name, data, id, self.username, self.password)
        return result


class Retrieve(object):
    def __init__(self, args):
        self.args = args
        self.init_data = self.args["init_data"]
        self.esds_id = self.args["esds_id"]
        self.inteligentsearch_url = self.args["inteligentsearch_url"]
        self.embed_url = self.args["embed_url"]
        self.dbguid = self.args.get("dbguid", None)
        self.es_url = self.args["es_url"]
        self.username = self.args["username"]
        self.password = self.args["password"]

        self.encode_engine = Restful(args["embed_url"]) if "embed_url" in args else None
        self.reranker_engine = Restful(args["reranker_url"]) if "reranker_url" in args else None

    @staticmethod
    def process_index_name(index_name):
        # 检查是否为 list
        if isinstance(index_name, list):
            result_comma = ','.join(index_name)
            result_semicolon = ';'.join(index_name)
        elif isinstance(index_name, str):
            # 使用正则表达式替换所有分隔符为逗号
            result_comma = ','.join(re.split(r'[;\\:。.-]', index_name))
            result_semicolon = ';'.join(re.split(r'[,\\:。.-]', index_name))
        else:
            raise ValueError(" [*] Please check if the index_name. \n Make sure is str or list")
        return result_comma, result_semicolon


    def data_search(self, data, index_name):
        """
        文件查询方式
        Parameters
        ----------
        data es接口查询数据入参
        index_name 索引名称

        Returns
        -------

        """
        tik = time.time()
        result_comma, result_semicolon = self.process_index_name(index_name)
        self.es_search_url = self.es_url + "/" + result_comma + "/_search"
        headers = {
            'Content-Type': 'application/json'
        }

        response = requests.request("POST", self.es_search_url, headers=headers,
                                    auth=HTTPBasicAuth(self.username, self.password), verify=False,
                                    data=json.dumps(data, indent=4, ensure_ascii=False).encode("utf-8"))

        try:
            re = json.loads(response.text)
            if "error" in re:
                error = re["error"]["type"]
                print(f"[*] search应用中， 数据查询错误，请检查传参，错误类型{error}")
                outs = []
            else:
                outs = json.loads(response.text)["hits"]["hits"]
        except Exception as e:
            print(f"[*] search应用中，es 查询数据错误，请检查es地址是否访问通，错误类型{e}，es接口返回{response.text}")
            outs = []

        elapse = time.time() - tik
        return outs, elapse

    def getsimilarnew_condition(self, select_conditions):
        if select_conditions:
            condition = [
                {
                    "conditionList": [{key: item} for item in
                                      (value if isinstance(value, list) and value else [value]) if
                                      item],
                    "highlights": "false"
                }
                for key, value in select_conditions.items() if value not in [[], ""]
            ]
            if not condition:
                condition = ""
        else:
            condition = ""
        return condition

    def getes_condition(self, select_conditions):
        if select_conditions:
            query_condition = " && ".join(
                [
                    f"({' || '.join([f'{key}_keyword:{v}' for v in (value if isinstance(value, list) else [value]) if v])})"
                    for key, value in select_conditions.items() if value and any(value)
                ]
            )
        else:
            query_condition = ""
        return query_condition


    def getsimilarnew_params(self, select_field, question, index_name, includes, pagesize, condition, regexp_content, non_regexp_content):
        result_comma, result_semicolon = self.process_index_name(index_name)
        data = {
            "wd": question,
            "fields": select_field,
            "re_fields": includes,
            "vecfield": "contentvec",
            "cnums": result_semicolon,
            "pn": 0,
            "rn": pagesize,
            "vecType": "1",
            "pluginName": "EmbeddingOperatorNew",
            "isSynonym": "1",
            "esdsid": self.esds_id,
            "accuracy": 15,
            "rescore": "{'inteligentsearchType':'3','sim1':'contentinfo'}",
            "opCondition": condition,
            "opType": "0"
        }

        # 增加全文检索插件正则表达式
        if regexp_content is None and non_regexp_content is None:
            data_request = data
        else:
            regexpCondition = [{"conditionList": [d for d in
                                                  [{"contentinfo": regexp_content},
                                                   {"-contentinfo": non_regexp_content}] if
                                                  d and any(v is not None for v in d.values())], "highlights": False}]
            data["regexpCondition"] = regexpCondition
            data["regexpType"] = 1
            data_request = data
        return data_request


    def get_es_params_1(self, select_field, query_condition, question, pageSize, includes, regexp_content, non_regexp_content):
        regexp = self.genExpParam(regexp_content)
        must_not = self.genMustNotParam(non_regexp_content)
        query_string = self.commonQuery(query_condition)
        query = {
            "bool": {
                "must": [
                    {
                        "more_like_this": {
                            "fields": [
                                select_field  # 检索字段
                            ],
                            "like": [
                                question
                            ],
                            "max_query_terms": 25,
                            "min_term_freq": 1,
                            "min_doc_freq": 1,
                            "max_doc_freq": 2147483647,
                            "min_word_length": 0,
                            "max_word_length": 0,
                            "minimum_should_match": "50%",
                            "boost_terms": 0.0,
                            "include": "false",
                            "fail_on_unsupported_field": "true",
                            "boost": 1.0
                        }
                    }
                ],
                "adjust_pure_negative": "true",
                "boost": 1.0
            }
        }
        if query_condition:
            query["bool"]["must"].append(query_string)
        if regexp:
            query["bool"]["must"].append(regexp)
        if must_not:
            query["bool"]["must_not"] = must_not
        data = {
            "from": 0,
            "size": pageSize,
            "query": query,
            "_source": {
                "includes": includes,
                "excludes": []
            },
            "track_total_hits": 2147483647
        }
        return data


    def get_es_params_2(self, query_condition, question2vec, pageSize, includes, regexp_content, non_regexp_content):
        regexp = self.genExpParam(regexp_content)
        must_not = self.genMustNotParam(non_regexp_content)
        query_string = self.commonQuery(query_condition)
        query = {
            "script_score": {
                "query": {
                    "bool": {
                        "must": [

                        ],
                        "adjust_pure_negative": "true",
                        "boost": 1.0
                    }
                },
                "script": {
                    "source": "cosineSimilarity(params.queryVector, doc['contentvec_vector'])+1",
                    "lang": "painless",
                    "params": {
                        "queryVector": question2vec
                    }
                },
                "boost": 1.0
            }
        }
        if query_condition:
            query["script_score"]["query"]["bool"]["must"].append(query_string)
        if regexp:
            query["script_score"]["query"]["bool"]["must"].append(regexp)
        if must_not:
            query["script_score"]["query"]["bool"]["must_not"] = must_not
        data = {
            "from": 0,
            "size": pageSize,
            "query": query,
            "_source": {
                "includes": includes,
                "excludes": []
            },
            "track_total_hits": 2147483647
        }
        return data

    def genExpParam(self, regexp_content):
        if not regexp_content:
            return None
        return {
            "regexp": {
                "contentinfo_keyword": {
                    "value": f".*({regexp_content}).*",
                    "max_determinized_states": 1000000
                }
            }
        }

    def genMustNotParam(self, non_regexp_content):
        if not non_regexp_content:
            return None
        return [
            {
                "term": {
                    "deleted_boolean": {
                        "value": True,
                        "boost": 1.0
                    }
                }
            },
            {
                "regexp": {
                    "contentinfo_keyword": {
                        "value": f".*({non_regexp_content}).*"
                    }
                }
            }
        ]

    def commonQuery(self, query_condition):
        return {
            "query_string": {
                "query": query_condition,
                # 删选条件 # "query": "(isenable_integer:0 || isenable_integer:1) && (isenable_integer:0 || isenable_integer:1)"
                "fields": [],
                "type": "best_fields",
                "default_operator": "and",
                "max_determinized_states": 10000,
                "enable_position_increments": "true",
                "fuzziness": "AUTO",
                "fuzzy_prefix_length": 0,
                "fuzzy_max_expansions": 50,
                "phrase_slop": 0,
                "escape": "false",
                "auto_generate_synonyms_phrase_query": "true",
                "fuzzy_transpositions": "true",
                "boost": 1.0
            }
        }

    def get_es_params_3(self, select_field, question, query_condition, question2vec, pageSize, includes, regexp_content, non_regexp_content):
        regexp = self.genExpParam(regexp_content)
        must_not = self.genMustNotParam(non_regexp_content)
        query_string = self.commonQuery(query_condition)
        query = {
            "script_score": {
                "query": {
                    "bool": {
                        "must": [
                            {
                                "more_like_this": {
                                    "fields": [
                                        select_field
                                    ],
                                    "like": [
                                        question
                                    ],
                                    "max_query_terms": 25,
                                    "min_term_freq": 1,
                                    "min_doc_freq": 1,
                                    "max_doc_freq": 2147483647,
                                    "min_word_length": 0,
                                    "max_word_length": 0,
                                    "minimum_should_match": "50%",
                                    "boost_terms": 0.0,
                                    "include": "false",
                                    "fail_on_unsupported_field": "true",
                                    "boost": 1.0
                                }
                            }
                        ],
                        "adjust_pure_negative": "true",
                        "boost": 1.0
                    }
                },
                "script": {
                    "source": "cosineSimilarity(params.queryVector, doc['contentvec_vector'])+1",
                    "lang": "painless",
                    "params": {
                        "queryVector": question2vec
                    }
                },
                "boost": 1.0
            }
        }
        if query_condition:
            query["script_score"]["query"]["bool"]["must"].append(query_string)
        if regexp:
            query["script_score"]["query"]["bool"]["must"].append(regexp)
        if must_not:
            query["script_score"]["query"]["bool"]["must_not"] = must_not
        data = {
            "from": 0,
            "size": pageSize,
            "query": query,
            "_source": {
                "includes": includes,
                "excludes": []
            },
            "track_total_hits": 2147483647
        }
        return data

    def getsimilarnew_search(self, params):

        headers = {
            'Content-Type': 'application/json',
            "Authorization": "Bearer " + "token",
            "User-Agent": "python"
        }
        tik = time.time()
        response = requests.post(url=self.inteligentsearch_url + "/esinteligentsearch/getByVector",
                                 data=json.dumps(params), headers=headers)
        try:
            re = json.loads(response.text)
            outs = eval(json.dumps(json.loads(re["content"])["result"]["records"]))
        except Exception as e:
            print(f"[*] search应用中，es 查询数据错误，请检查es地址是否访问通，错误类型{e}，es接口返回{response.text}")
            outs = []
        elapse = time.time() - tik
        return outs, elapse

    @staticmethod
    def str2bool(v):
        if isinstance(v, bool) or isinstance(v, int):
            return bool(v)
        return v.lower() in ("true", "t", "1")

    @staticmethod
    def sigmoid(x):
        return 1 / (1 + math.exp(-x))

    def pos_es_results(self, results):
        if results == []:
            return []
        outs = []
        for i, re in enumerate(results):
            score = re["_score"]
            source = re["_source"]
            content = source["content_text"]
            sort = i + 1
            dbguid = source["dbguid_keyword"]
            outs.append([content, sort, dbguid, self.sigmoid(score)])
        return outs

    def pos_sim_results(self, results):
        outs = []
        for result in results:
            sort = result["sort"]
            dbguid = result["dbguid"]
            content = result["content"]
            score = result["_score"]
            outs.append([content, sort, dbguid, self.sigmoid(score)])
        return outs

    def __call__(self, question, index_name):
        if self.encode_engine is None:
            raise ValueError(f"[*] Please check embedding url is configured and correct!")

        data_embed = {"text": str(question)}
        embed_results = self.encode_engine.get_results(data_embed)
        if int(embed_results["status"]["code"]) != 0:
            error_msg = embed_results["status"]["error_msg"]
            raise ValueError(f"[*] Please check embedding url is correct!, error_msg:{error_msg}")

        # 参数传入
        question2vec = embed_results["result"][0]
        retrieval_method = int(self.args.get("retrieval_method", int(3)))
        pageSize = int(self.args.get("pageSize", int(10)))
        non_regexp_content = self.args.get("non_regexp_content", None)
        regexp_content = self.args.get("regexp_content", None)
        is_reranker = self.str2bool(self.args.get("isreranker", False))
        select_conditions = self.args.get("select_condition", None)

        if retrieval_method not in [1, 2, 3, 4]:
            raise ValueError(f"[*] retrieval_method only support one of 1,2,3,4,but got {retrieval_method}!")

        # 检索数据
        if retrieval_method == 4:  # 全文检索
            condition = self.getsimilarnew_condition(select_conditions)
            fields = "content;dbguid"
            select_field = "content"
            params = self.getsimilarnew_params(select_field, question, index_name, fields, pageSize, condition,
                                                        regexp_content, non_regexp_content)

            results, elapse = self.getsimilarnew_search(params)
            pos_results = self.pos_sim_results(results)


        else:
            fields = [
                "dbguid_keyword", "content_text"
            ]
            select_field = "content_text"
            condition = self.getes_condition(select_conditions)
            # 向量检索、关键词、混合
            if retrieval_method == 1:  # 关键字检索
                params = self.get_es_params_1(select_field, condition, question, pageSize, fields, regexp_content,
                                              non_regexp_content)
            elif retrieval_method == 2:  # 向量检索
                params = self.get_es_params_2(condition, question2vec, pageSize, fields, regexp_content,
                                              non_regexp_content)
            elif retrieval_method == 3:  # 混合检索
                params = self.get_es_params_3(select_field, question, condition, question2vec, pageSize, fields,
                                              regexp_content, non_regexp_content)

            results, elapse = self.data_search(params, index_name)
            pos_results = self.pos_es_results(results)

        if is_reranker:
            if self.reranker_engine is None:
                raise ValueError(f"[*] Please check reranker url is configured and correct!")
            text_list = [pre[0] for pre in pos_results]
            data_reranker = {"text": str(question), "compare_list": text_list}
            t0k = time.time()
            reranker_results = self.reranker_engine.get_results(data_reranker)

            if int(reranker_results["status"]["code"]) != 0:
                error_msg = reranker_results["status"]["error_msg"]
                raise ValueError(f"[*] Please check reranker url is correct!, error_msg:{error_msg}")

            results_reranker = reranker_results["result"]
            print(f"[*] Time for File rerankering: {time.time() - t0k:.3f}s")
            final_results = []
            for i, re_reranker in enumerate(results_reranker):
                content, score = re_reranker[0], re_reranker[1]
                sort = pos_results[text_list.index(content)][1]
                dbguid = pos_results[text_list.index(content)][2]
                final_results.append([content, sort, dbguid, self.sigmoid(score)])
        else:
            final_results = pos_results
        re_results = {
            "retrieve_result":
                sorted([{"content": r[0], "sort": i+1, "score": r[3], "dbguid": r[2]} for i, r in enumerate(final_results)], key=lambda x: x["score"], reverse=True)}
        return re_results

def parse_word_document(doc_path):
    doc = Document(doc_path)
    titles = []
    text = ''
    i=1
    for paragraph in doc.paragraphs:
        title = isTitle(paragraph)

        # 不是标题
        if title is None:
            text += paragraph.text
            text += '\n'
        # # 是标题
        else:
            if text!="" and text!="\n":
               #根据正文获取关键词
               keywords = getKeywords(text)
               i=i+1
            text=""
            # titles.append(text)
            # text = ''
            # text += paragraph.text
            # text += '\n'
    print(str(i) + "########" + text)
    return titles


def getOutlineLevel(inputXml):
    """
    功能 从xml字段中提取出<w:outlineLvl w:val="number"/>中的数字number
    参数 inputXml
    返回 number
    """
    start_index = inputXml.find('<w:outlineLvl')
    end_index = inputXml.find('>', start_index)
    number = inputXml[start_index:end_index + 1]
    number = re.search("\d+", number).group()
    return number

def getKeywords(content):
    for i in range(5):
        try:
            messages = [{
                "role": "user",
                "content": get_prompt("getkeywords2_template.txt", content, [])
            }]
            response = qwen7b(messages)
            return json.loads(response)["keywords"]
        except :
            continue


def get_paragraph_number(para):
    # 获取段落的 XML 结构
    xml = para._element
    # 寻找带编号的 XML 标记，编号一般存储在<w:numPr>中
    num_pr = xml.find('.//w:numPr', namespaces=xml.nsmap)
    
    if num_pr is not None:
        # 通过XML解析获取编号信息
        num_id = num_pr.find('.//w:numId', namespaces=xml.nsmap)
        if num_id is not None:
            num_id_text = num_id.text
            if num_id_text:
                return num_id_text
        # 如果没有直接的 numId，尝试获取层级信息
        lvl = num_pr.find('.//w:ilvl', namespaces=xml.nsmap)
        if lvl is not None:
            return f"Level {lvl.text}"  # 如果没有 numId，则返回层级信息
    return None

def isTitle(paragraph):
    """
    功能 判断该段落是否设置了大纲等级
    参数 paragraph:段落
    返回 None:普通正文，没有大纲级别 0:一级标题 1:二级标题 2:三级标题
    """
    # 如果是空行，直接返回None
    if paragraph.text.strip() == '':
        return None,""

    # 如果该段落是直接在段落里设置大纲级别的，根据xml判断大纲级别
    paragraphXml = paragraph._p.xml
    if paragraphXml.find('<w:outlineLvl') >= 0:
        return getOutlineLevel(paragraphXml),paragraph.text
    # 如果该段落是通过样式设置大纲级别的，逐级检索样式及其父样式，判断大纲级别
    targetStyle = paragraph.style
    while targetStyle is not None:
        # 如果在该级style中找到了大纲级别，返回
        if targetStyle.element.xml.find('<w:outlineLvl') >= 0:
            return getOutlineLevel(targetStyle.element.xml),paragraph.text
        else:
            targetStyle = targetStyle.base_style
    # 如果在段落、样式里都没有找到大纲级别，返回None
    return None,""

def queryGraph():
    url = "http://**************:35968/relation_scztdj/gremlin"

    payload = "{\"params\":{\"gremlin\":\"g.V().hasLabel('ANSWER').repeat(inE().otherV()).until(__.inE().count().is(0)).simplePath().path().toList()\"}}"
    headers = {'user-agent': 'vscode-restclient'}

    ret =[]
    response = requests.request("POST", url, data=payload, headers=headers)
    for item in response.json()["result"]["data"]:
        answerid = ""#//答案id
        path = ""#//路径
        desc =""
        for i in range(0, len(item["objects"])):
            obj = item["objects"][i]
            name = ""
            if obj["type"] == "vertex":
                label = obj["label"]
                id = obj["id"]
                name = obj["properties"]["name"][0]["value"]
                if "description" in obj["properties"]:
                    desc = desc + obj["properties"]["description"][0]["value"]
                if "ANSWER"==label:
                    answerid = id
            elif obj["type"] == "edge":
                if "edge_name" in obj["properties"]:
                    if "value" in obj["properties"]["edge_name"]:
                        name = obj["properties"]["edge_name"]["value"]

            if  i == len(item["objects"])- 1:
                if name != "":
                    path = name + path
            else:
                if name != "":
                    path = "-" + name + path
        aa ={}
        aa["path"] = path
        aa["id"] = answerid
        aa["desc"] = desc
        ret.append(aa)
    return ret
  
args = {}
args["init_data"] = {
    "columnconfigs": [
        {
            "field": "id",
            "name": "主键",
            "type": "_keyword"
        },
        {
            "field": "infodate",
            "name": "信息日期",
            "type": "_date"
        },
        {
            "field": "userguid",
            "name": "用户guid",
            "type": "_keywords"
        },
        {
            "field": "syscategory",
            "name": "系统分类",
            "type": "_keyword"
        },
        {
            "field": "kworg",
            "name": "关键词，原文",
            "type": "_keyword"
        },
        {
            "field": "kw",
            "name": "关键词，分词",
            "type": "_text"
        },
        {
            "field": "kwvec",
            "name": "关键词，向量",
            "type": "_vector"
        },
        {
            "field": "content",
            "name": "文字块",
            "type": "_text"
        },
        {
            "field": "contentvec",
            "name": "向量",
            "type": "_vector"
        },

        {
            "field": "pathorg",
            "name": "路径，原文",
            "type": "_keyword"
        },
        {
            "field": "path",
            "name": "路径，分词",
            "type": "_text"
        },
        {
            "field": "pathvec",
            "name": "路径，向量",
            "type": "_vector"
        },
        {
            "field": "graphid",
            "name": "图谱id",
            "type": "_keyword"
        }
    ],
    "category": {
        "categoryDescribe": "nzyxzrgs23",
        "categoryName": "nzyxzrgs23",
        "categoryNum": "epointvecknowledge",
        "categoryType": "",
        "es_shards": 3,
        "es_weight": 0,
        "isAddTag": 0,
        "isHotUpdate": False,
        "isOpen": 1,
        "isSaveHotWords": False,
        "ouGuid": "",
        "tagNum": "",
        "tagSource": ""
    },
    "parentcnum": ""
}
args["embed_url"] = "http://192.168.186.18:9081/linezhengshi_embedding/embed"
args["es_url"] = "https://192.168.186.63:9200"
args["username"] = "admin"
args["password"] = "admin"
args["esds_id"] = 8
args["inteligentsearch_url"] = "http://192.168.186.65/inteligentsearch/rest"
args["reranker_url"] = "http://192.168.186.112:8080/epoint-gateway/linezhengshi_reranker/predict"
args["isreranker"] = True
args["pageSize"] = 5
index_name = "nzyxzrgs19"   
args["init_data"]["category"]["categoryDescribe"] = index_name
args["init_data"]["category"]["categoryName"] = index_name


def parse_txt_to_list(file_path):
    with open(file_path, 'r', encoding='utf-8') as file:
        lines = file.readlines()

    result = []
    current_chapter = None
    current_section = None
    section_content = []

    for line in lines:
        line = line.strip()
        # 匹配章
        chapter_match = re.match(r'第(.+?)章、(.+)', line)
        if chapter_match:
            if current_chapter and section_content:
                # 保存上一个节的内容
                result.append({
                    "path": f"{current_chapter}-{current_section}" if current_section else current_chapter,
                    "content": "\n".join(section_content)
                })
                section_content = []

            current_chapter = chapter_match.group(0)
            current_section = None  # 重置节

        # 匹配节
        section_match = re.match(r'第(.+?)节、(.+)', line)
        if section_match:
            if current_section and section_content:
                # 保存上一个节的内容
                result.append({
                    "path": f"{current_chapter}-{current_section}",
                    "content": "\n".join(section_content)
                })
                section_content = []

            current_section = section_match.group(0)

        # 匹配条或其他内容
        elif line:
            section_content.append(line)

    # 保存最后的内容
    if section_content:
        result.append({
            "path": f"{current_chapter}-{current_section}" if current_section else current_chapter,
            "content": "\n".join(section_content)
        })

    return result


if __name__ == "__main__":
    #  删除时，需要手动到ES平台中， 数据集成->统一元数据->元数据->元数据管理，搜索到了，删除，再到回收站，删除一下
    index_name= "nzyxzrgs27"
    createIndex(index_name)

    #查询图谱获取所有问题及路径
    questionlist = queryGraph()
    for object in questionlist:
        path = object["path"]
        graphid = object["id"]
        desc = object["desc"]
        keywords = getKeywords(object["desc"])
        # 数据导入
        # result = question2es(desc, index_name,path,graphid,keywords)
        result = insertData(desc, index_name,path,graphid,keywords)
