import json
from collections import defaultdict
from typing import List, Dict, Any
import requests
from epointml.utils import DbPoolUtil


class ESApiClient:
    def __init__(self, cookies: str = None):
        # 可以传入自定义 cookies，否则使用默认值
        self.cookies = cookies or "_font_size_ratio_=1.0; epoint_local=zh_CN; access_token_expiresin=1800; _idea_skin_=cyanine; _theme_=idea; sid=b54db41728174cec861f2773df75aac1-zwbg-soa; EPTOKEN=D2C046E214A9C2EBDA77E3FA65700019B161F2802006EF2057BD430C901E3F9B; access_token=b54db41728174cec861f2773df75aac1-zwbg-soa; refresh_token=d5033ad948dbf40a007f7145babf22f4-zwbg-soa"
        self.jdbc_url = "*******************************************************************************************************************"

    def call_es_api(self, data: Dict[str, Any]) -> Dict[str, Any]:
        url = "http://192.168.173.99:8915/EpointFrame/rest/dynamicapi/calles_table"
        headers = {
            "Content-Type": "application/json",
            "Cookie": self.cookies
        }
        response = requests.post(url, data=json.dumps(data), headers=headers)
        return response.json()

    def query_qa_db(self, tablename: str) -> Dict[str, Any]:
        # db = DbPoolUtil(jdbcurl=self.jdbc_url)
        # sql = f"SELECT question, answer FROM znws_askdetail_qa WHERE 1=1 and tableguid={tableguid}"
        # qa_list = db.execute_query(sql)
        # return qa_list
        fieldlist = ""
        tableguid = ""
        qa_list = []
        if tablename == '主体诚信库信息表':
            fieldlist = [
                {
                    "fieldname": "dwmx",
                    "fieldnamecn": "单位名称"
                },
                {
                    "fieldname": "sfshtg",
                    "fieldnamecn": "是否审核通过"
                },
                {
                    "fieldname": "shtgsj",
                    "fieldnamecn": "审核通过时间"
                },
                {
                    "fieldname": "shr",
                    "fieldnamecn": "审核人"
                },
                {
                    "fieldname": "dwlx",
                    "fieldnamecn": "单位类型"
                },
                {
                    "fieldname": "rowguid",
                    "fieldnamecn": "主键"
                }
            ]
            tableguid = "41b7b22a-e184-4ffa-9b83-3c1bd51e204b"
            tablename = "znws_askdetail_ztcxkxx"
            qa_list = [
                {
                    "question": "南京南咨工程咨询有限公司、无锡德力物业管理有限公司、海南中顺福海运有限公司诚信库是否审核通过",
                    "answer": "南京南咨工程咨询有限公司（招标人）已审核通过，审核通过时间为2024-09-13 10:31:25，审核人为谈月娇。<br>无锡德力物业管理有限公司（招标代理）尚未审核通过，审核不通过时间为2025-02-24 10:45:26，审核人为谈月娇。<br>海南中顺福海运有限公司（招标代理）尚未审核通过。"
                }
            ]
        elif tablename == '标段维度表':
            tableguid = 'e528021c-59dc-4463-99f3-bda78e8afe1d'
            fieldlist = [
                {
                    "fieldname": "biaoduanguid",
                    "fieldnamecn": "BIAODUANGUID"
                },
                {
                    "fieldname": "bdno",
                    "fieldnamecn": "标段编号"
                },
                {
                    "fieldname": "bdname",
                    "fieldnamecn": "标段名称"
                },
                {
                    "fieldname": "zbdl",
                    "fieldnamecn": "招标代理"
                },
                {
                    "fieldname": "zbr",
                    "fieldnamecn": "招标人"
                },
                {
                    "fieldname": "xmlb",
                    "fieldnamecn": "项目类别"
                },
                {
                    "fieldname": "jbr",
                    "fieldnamecn": "经办人"
                },
                {
                    "fieldname": "cjjg",
                    "fieldnamecn": "成交价格"
                },
                {
                    "fieldname": "fwje",
                    "fieldnamecn": "服务费"
                },
                {
                    "fieldname": "xjfwfsfqr",
                    "fieldnamecn": "询价服务费是否确认"
                },
                {
                    "fieldname": "cjlxshtgsj",
                    "fieldnamecn": "采购立项审核通过时间"
                },
                {
                    "fieldname": "zbggshtgsj",
                    "fieldnamecn": "招标公告/邀请函审核通过时间"
                },
                {
                    "fieldname": "zbgsshtgsj",
                    "fieldnamecn": "中标公示审核通过时间"
                },
                {
                    "fieldname": "zbtzsshtgsj",
                    "fieldnamecn": "中标通知书审核通过时间"
                },
                {
                    "fieldname": "zbgskssj",
                    "fieldnamecn": "中标公示开始时间"
                },
                {
                    "fieldname": "zbgsjssj",
                    "fieldnamecn": "中标公示结束时间"
                },
                {
                    "fieldname": "kbsj",
                    "fieldnamecn": "开标时间"
                },
                {
                    "fieldname": "pjsj",
                    "fieldnamecn": "评标时间"
                },
                {
                    "fieldname": "xmcjsj",
                    "fieldnamecn": "项目创建时间"
                },
                {
                    "fieldname": "pbjssj",
                    "fieldnamecn": "评标结束时间"
                },
                {
                    "fieldname": "zfgjrjsyfsl",
                    "fieldnamecn": "支付工具软件使用费数量"
                },
                {
                    "fieldname": "xzzbwjsl",
                    "fieldnamecn": "下载招标文件数量"
                },
                {
                    "fieldname": "jnbzjsl",
                    "fieldnamecn": "缴纳保证金数量"
                },
                {
                    "fieldname": "sfhtba",
                    "fieldnamecn": "是否合同备案"
                },
                {
                    "fieldname": "sfhtgd",
                    "fieldnamecn": "是否合同归档"
                },
                {
                    "fieldname": "rowguid",
                    "fieldnamecn": "主键"
                },
                {
                    "fieldname": "shr",
                    "fieldnamecn": "审核人"
                },
                {
                    "fieldname": "dwmc",
                    "fieldnamecn": "单位名称"
                }
            ]
            tablename = "znws_askdetail_bdwd"
            qa_list = [
                {
                    "question": "帮我查询曹阳经办的所有项目",
                    "answer": "曹阳经办的项目如下："
                }
            ]
        return qa_list