#!/usr/bin/env node
import { Server } from '@modelcontextprotocol/sdk/server/index.js';
import { StdioServerTransport } from '@modelcontextprotocol/sdk/server/stdio.js';
import {
  CallToolRequestSchema,
  ErrorCode,
  ListToolsRequestSchema,
  McpError,
} from '@modelcontextprotocol/sdk/types.js';

const isValidKnowledgeQueryArgs = (
  args: any
): args is { question: string } =>
  typeof args === 'object' &&
  args !== null &&
  typeof args.question === 'string';

class KnowledgeQueryServer {
  private server: Server;

  constructor() {
    this.server = new Server(
      {
        name: 'knowledge-query-server',
        version: '0.1.0',
      },
      {
        capabilities: {
          resources: {},
          tools: {},
        },
      }
    );

    this.setupToolHandlers();

    this.server.onerror = (error) => console.error('[MCP Error]', error);
    process.on('SIGINT', async () => {
      await this.server.close();
      process.exit(0);
    });
  }

  private setupToolHandlers() {
    this.server.setRequestHandler(ListToolsRequestSchema, async () => ({
      tools: [
        {
          name: 'knowledge_query',
          description: '根据用户问题进行知识查询（查询逻辑由用户后续实现）',
          inputSchema: {
            type: 'object',
            properties: {
              question: {
                type: 'string',
                description: '用户问题',
              },
            },
            required: ['question'],
          },
        },
      ],
    }));

    this.server.setRequestHandler(CallToolRequestSchema, async (request) => {
      if (request.params.name !== 'knowledge_query') {
        throw new McpError(
          ErrorCode.MethodNotFound,
          `Unknown tool: ${request.params.name}`
        );
      }

      if (!isValidKnowledgeQueryArgs(request.params.arguments)) {
        throw new McpError(
          ErrorCode.InvalidParams,
          'Invalid arguments for knowledge_query'
        );
      }

      // 占位返回，后续由用户实现具体查询逻辑
      return {
        content: [
          {
            type: 'text',
            text: `收到问题: ${request.params.arguments.question}（此处为占位返回，具体查询逻辑请补充实现）`,
          },
        ],
      };
    });
  }

  async run() {
    const transport = new StdioServerTransport();
    await this.server.connect(transport);
    console.error('Knowledge Query MCP server running on stdio');
  }
}

const server = new KnowledgeQueryServer();
server.run().catch(console.error);
