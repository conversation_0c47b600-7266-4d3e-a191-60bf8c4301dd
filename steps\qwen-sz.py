# -*- coding: utf-8 -*-
"""
Description:

@Author: ssjie

Created on: 2024/12/3
"""
import time
import json
import requests
import base64


def test(item):
    url = "http://************:1025/v1/chat/completions"
    messages = [
    {
        "role": "user",
        "content": """"""
    },
        # {"role":"user","content":"经营范围变更后需要办理营业执照吗"},
        # {"role": "assistant", "content": "经营范围变更后需要办理营业执照,jsajsadsadsadnsa {\\\"role\\\":\\\"user\\\",\\\"content\\\":\\\"经营范围变更\\\"},asjdsajdlkadjaajssa经营范围变更后需要办理营业执照,jsajsadsadsadnsa {\\\"role\\\":\\\"user\\\",\\\"content\\\":\\\"经营范围变更\\\"},asjdsajdlkadjaajssa经营范围变更后需要办理营业执照,jsajsadsadsadnsa {\\\"role\\\":\\\"user\\\",\\\"content\\\":\\\"经营范围变更\\\"},asjdsajdlkadjaajssa经营范围变更后需要办理营业执照,jsajsadsadsadnsa {\\\"role\\\":\\\"user\\\",\\\"content\\\":\\\"经营范围变更\\\"},asjdsajdlkadjaajssa经营范围变更后需要办理营业执照,jsajsadsadsadnsa {\\\"role\\\":\\\"user\\\",\\\"content\\\":\\\"经营范围变更\\\"},asjdsajdlkadjaajssa经营范围变更后需要办理营业执照,jsajsadsadsadnsa {\\\"role\\\":\\\"user\\\",\\\"content\\\":\\\"经营范围变更\\\"},asjdsajdlkadjaajssa经营范围变更后需要办理营业执照,jsajsadsadsadnsa {\\\"role\\\":\\\"user\\\",\\\"content\\\":\\\"经营范围变更\\\"},asjdsajdlkadjaajssa"},
        #  {"role": "user", "content": "需要如何办理？"},
        # {"role": "assistant",
        #  "content": "经营范围变更后需要办理营业执照,jsajsadsadsadnsa {\\\"role\\\":\\\"user\\\",\\\"content\\\":\\\"经营范围变更\\\"},asjdsajdlkadjaajssa经营范围变更后需要办理营业执照,jsajsadsadsadnsa {\\\"role\\\":\\\"user\\\",\\\"content\\\":\\\"经营范围变更\\\"},asjdsajdlkadjaajssa经营范围变更后需要办理营业执照,jsajsadsadsadnsa {\\\"role\\\":\\\"user\\\",\\\"content\\\":\\\"经营范围变更\\\"},asjdsajdlkadjaajssa经营范围变更后需要办理营业执照,jsajsadsadsadnsa {\\\"role\\\":\\\"user\\\",\\\"content\\\":\\\"经营范围变更\\\"},asjdsajdlkadjaajssa经营范围变更后需要办理营业执照,jsajsadsadsadnsa {\\\"role\\\":\\\"user\\\",\\\"content\\\":\\\"经营范围变更\\\"},asjdsajdlkadjaajssa经营范围变更后需要办理营业执照,jsajsadsadsadnsa {\\\"role\\\":\\\"user\\\",\\\"content\\\":\\\"经营范围变更\\\"},asjdsajdlkadjaajssa经营范围变更后需要办理营业执照,jsajsadsadsadnsa {\\\"role\\\":\\\"user\\\",\\\"content\\\":\\\"经营范围变更\\\"},asjdsajdlkadjaajssa"},
        # {"role": "user", "content": "请以中国企业为例进行说明"}
    ]

    data = {
        "messages": messages,
        "stream": False,
        "model":"deepseekr1"
    }

    response = requests.request("POST", url, data=json.dumps(data), stream=False)

    print(json.loads(response.text)['choices'][0]['message']['content'])

if __name__ == '__main__':
    lst = [
        "大学生深圳短期住宿费用太高，有什么解决办法？",
        "应届生预算有限，哪里能找到免押金的住处？",
        "听说深圳住宿费一天要300+，学生党怎么负担？",
        "来深面试一周，住宿费比路费还贵怎么办？",
        "有没有针对毕业生的低价过渡性住房？",
        "深圳租房中介费太贵，官方有替代方案吗？",
        "短期实习不包住，公司附近租房太贵怎么办？",
        "毕业生来深找工作，如何避免被黑中介坑钱？",
        "深圳青年旅社床位紧张，还有其他选择吗？",
        "免费住宿政策听说延长了，现在能住多久？",
        "第一次来深圳面试，住哪里方便又安全？",
        "跨省求职需要提前到深圳准备，住哪里合适？",
        "深圳有没有带求职指导的公益住宿项目？",
        "参加多场面试，住处能按企业位置调整吗？",
        "毕业生来深找工作，政府有住宿帮扶吗？",
        "港澳学生来深就业，哪里提供过渡性住所？",
        "海外留学生回国求职，深圳有接待政策吗？",
        "深漂初期没收入，如何解决住宿燃眉之急？",
        "深圳哪些区求职资源多且住宿成本低？",
        "免费住宿期间能否参加本地招聘会？",
        "青年驿站听说升级了，现在有什么新福利？",
        "听说现在免费住15天，怎么申请？",
        "初到深圳人生地不熟，哪里能快速安顿？"
    ]
    # for i in lst:
    #     print("问题：" + i)
    #     test(i)
    test('')