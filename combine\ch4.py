#  准备按最新的 剧本实现逻辑 https://docs.qq.com/sheet/DZkVoTHliZkVCVElw?tab=9o3yeg

# 测试数据
# 你好机器人。你们政府窗口办事太不方便了，线下办事预约号太少了，我约了一个礼拜都没有约到。问问你，我是公司法定代表人，最近公司人事变动，有限公司变更法人代表需要提交什么材料？
# 那几天能办好？


import requests
import json
import os
import re
import sys
import os

# 获取根目录路径
root_path = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
sys.path.append(root_path)

from steps.tools import get_prompt, qwen7b,qwenMax,callKimi

def emocheck(query,chat_history):
    messages = [item for item in chat_history]
    
    messages.append( {
        "role": "user",
        "content": get_prompt("spearate_template.txt",query,[])
    })

    response = qwen7b(messages)
    dt = json.loads(response)
    return dt['寒喧'],dt['情绪问题'],dt['业务问题']



def InteractiveIntention(query,chat_history):
    messages = [ {
        "role": "user",
        "content": get_prompt("interactive_template.txt",query,chat_history)
    }]

    response = qwenMax(messages)

    dt = json.loads(response)
    return dt['交互类别'],dt['关键词']

def queryGraph(keywords):
    url = "http://192.168.186.65:5000/querygraph"

    payload = "{\"keywords\":\"" + keywords +"\",\"graphurl\":\"http://172.29.255.220:9001/relation_nzyxzrgs/gremlin\",\"index_name\":\"nzyxzrgs\"}"
    headers = {
        'user-agent': "vscode-restclient",
        'content-type': "application/json"
        }

    response = requests.request("POST", url, data=payload, headers=headers)

    print(response.text)
    return response.json()["graphlist"], response.json()["finalResult"]

def answerbyGraph(query,graphcontent,chat_history):
    prompt = get_prompt("answer_template.txt",query,[])
    prompt = prompt.replace("{graphcontent}", graphcontent)
    messages = [ {
        "role": "user",
        "content": prompt
    }]
    response = callKimi(messages)
    return response

def checkGraph(query, content):
    prompt = get_prompt("check_template.txt",query,[])
    prompt = prompt.replace("{content}", content)
    messages = [ {
        "role": "user",
        "content": prompt
    }]
    response = callKimi(messages)
    return response


chat_history =[]
g_assistant_response = None
status = None
status_table = []

while True:
    query = input("请输入: ")
    
    print("等待模型的回答ing...")
    greetings,emo,business = emocheck(query,chat_history)
    chat_history.append({
        "role": "user",
        "content": query
    })
    if greetings:
        print("回复:你好!")
        chat_history.append({
            "role": "assistant",
            "content": "你好!"
        })
    if emo:
        print("回复:您别着急，事情总会办成的。")
        chat_history.append({
            "role": "assistant",
            "content": "您别着急，事情总会办成的。!"
        })
        
    if not business:
        continue
    
    interactivetype,keywords = InteractiveIntention(business,chat_history)
    
    if interactivetype == "咨询":
        print("------------->>keywords:" + keywords)
        graphlist,txt = queryGraph(keywords)
        print("------------->>graphlist:\r\n" + "\r\n".join(graphlist))
        checkTag = "1"#checkGraph(business, txt)
        print(f"------------->>checkTag:{checkTag}")
        if checkTag == "1":
            ret = answerbyGraph(business,txt,chat_history)
            print(f"回复:{ret}")
            chat_history.append({
                "role": "assistant",
                "content": ret
            })
        else:
            print("回复:问题与谱图知识无关，请转人工！")
    
    elif interactivetype == "办理":
        print("回复:请您稍等，正在为您查询...")
    else:
        print("回复:未识别的类型," + interactivetype)
        



