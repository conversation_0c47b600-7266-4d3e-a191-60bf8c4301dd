import base64
import json

import pandas as pd
import requests
#批量测试千文+知识领域（信息要素）json格式

#知识基础（意图/指南等） txt后缀优于json后缀理解
file_path = r"./data/市场主体登记领域json.txt"

with open(file_path, "rb") as f:
    file_stream = f.read()
file_b64 = base64.b64encode(file_stream).decode("utf-8")
#地址（目前千问）
url = "http://192.168.186.18:8080/llm/predict"
#执行方法 file_path为需要处理的excel地址
def process_excel(file_path):
    # 读取Excel文件
    df = pd.read_excel(file_path, engine='openpyxl')
    # 遍历A列，拼接message
    for index, row in df.iterrows():
        # 获取A列的值作为问题
        question = row['question']
        prompt = f"""在我上传的JSON格式的知识图谱文件中，knowledgeDomains节点的下一层的节点代表了不同的知识领域，这些节点的name属性为【知识领域名称】，description属性为【知识领域描述】，
        informationelements节点为该知识领域相关的【信息要素】，这些节点的name属性为【信息要素名称】，description属性为【信息要素描述】，condition属性为【信息要素识别条件】
        你的角色：你是一个业务分析师，需要将问题拆解为后端需要的信息，输出需要的信息。
        你的能力：精准的判断力，强大的思维逻辑，严格的遵守规则。
        你的任务：
        1.先根据【知识领域描述】判断问题属于哪个知识领域。如果找不到对应的知识领域，请直接返回“无法判断您的知识领域”，如果能找到就先输出：知识领域:知识领域名称。
        然后查询该知识领域下的所有【信息要素】节点，遍历每个信息要素节点:
        根据每个信息要素节点的【信息要素识别条件】判断是否需要识别这个信息要素，
        如果需要识别，根据【信息要素描述】，用我输入的问题文字来匹配对应的选项，如果能匹配到唯一选项，输出匹配的唯一选项；如果无法匹配或者匹配多个选项则输出：需要询问”。
        如果不需要识别，直接输出：对应节点名称:无需识别。
        
        2、输出json格式示例：{{"知识领域":"xxxx","信息要素1":"xxxx","信息要素2":"xxxx"}}。
        3、指令2中json格式中的key的信息要素1和信息要素2替换成指令1中遍历的【信息要素名称】。如果指令1无法判断无法判断您的知识领域，则不需要输出信息要素。
        4、除json格式以外的内容禁止输出。
        问题是：{question}
        """
        # 拼接message消息体
        if(question):
            messages = [
                # {"role": "system", "content": "You're a helpful assistant"},
                {"role": "user",
                 "content": prompt}
            ]
            data = {
                "messages": messages,
                "files": [file_b64],  # 文件可以是多个
                "stream": False
            }
            response = requests.request("POST", url, data=json.dumps(data), stream=False)
            df['answer'] = df['answer'].astype(str)
            print(json.loads(response.text).get('result'))
            #把值塞到B列
            df.at[index, 'answer'] = json.loads(response.text).get('result')
    # 保存修改后的Excel文件
    df.to_excel(file_path, index=False, engine='openpyxl')


# 调用函数，传入文件绝对路径
file_path = r'。/data/question.xlsx'


process_excel(file_path)
