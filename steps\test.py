import asyncio
import time

import aiohttp

import time

import requests
import datetime
import json
def test():
    url = "http://10.137.69.12:1025/v1/chat/completions"
    messages = [
        {
            "role": "system",
            "content": """提示词"""
        }
        ,{"role":"user","content":"我要出国去大厅办护照，大厅收费300元，然后我去网上办的话才收150，我是不是在网上办最划算？"}
#         ,{"role":"assistant","content":"""根据您提供的知识库内容，以下是关于护照办理费用的解答：
# 核心问题解答： 您提到的120元、150元、300元三种护照办理费用，在现有官方信息中均无对应依据。根据《中国公民出境入境管理法》相关规定：
# 普通护照是公民出入境的基本证件（第10条）
# 办理护照属于法定义务（第2条）
# 特别提醒： 您问题中提到的港澳通行证办理（第29条）与护照属于不同证件类型。前往港澳地区应办理签注而非签证，两者收费标准不同。
# 办理建议：
# 请直接咨询深圳市公安局出入境管理局
# 官方网站：https://ga.sz.gov.cn/
# 服务热线：12367
# 线下办理地点：深圳市罗湖区解放路4018号出入境办证大厅
# 费用说明： 所有涉外证件收费标准均由国家发改委和财政部联合制定，建议通过上述官方渠道获取最新资费信息。"""}
#         , {"role": "user","content": "我听说120就能办啊"}
    ]

    data = {
        "messages": messages,
        "stream": False,
        "model": "deepseekr1"
    }

    response = requests.request("POST", url, data=json.dumps(data), stream=False)

    print(json.loads(response.text)['choices'][0]['message']['content'])


# 限制最大并发数（可选）
SEMAPHORE = asyncio.Semaphore(100)  # 限制最大100个并发

async def fetch(session, index):
    """异步请求 API"""
    async with SEMAPHORE:  # 控制并发数量
        try:
            async with test:
                return 1
        except Exception as e:
            print(f"Request {index} failed: {e}")
            return None

async def main():
    """创建并发任务"""
    async with aiohttp.ClientSession() as session:
        tasks = [fetch(session, i) for i in range(100)]  # 创建100个任务
        results = await asyncio.gather(*tasks)  # 并发执行
        return results

if __name__ == "__main__":
    test()
    # for i in range(10):
    #     print("==========================这是第"+i+"次接口重放==================================")
    #     starttime = time.time()
    #     asyncio.run(main())  # 运行主任务
    #     print("====本轮耗时===="+time.time() - starttime)