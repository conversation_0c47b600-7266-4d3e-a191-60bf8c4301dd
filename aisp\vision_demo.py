#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
视觉模型调用示例 - 单张图片分析
"""

import os
from test import analyze_image_with_qwen_vl

def demo_single_image():
    """演示分析单张图片"""
    # 指定要分析的图片路径
    image_path = "image/0印章0签名.jpg"
    
    # 检查图片是否存在
    if not os.path.exists(image_path):
        print(f"❌ 图片文件不存在: {image_path}")
        return
    
    print("🔍 开始分析图片...")
    print(f"📁 图片路径: {image_path}")
    print("=" * 50)
    
    # 分析图片 - 默认问题
    print("\n1️⃣ 使用默认问题分析:")
    result1 = analyze_image_with_qwen_vl(image_path)
    
    if result1.get("success"):
        print(f"✅ 分析成功")
        print(f"❓ 问题: {result1['question']}")
        print(f"📝 回答: {result1['response']}")
    else:
        print(f"❌ 分析失败: {result1.get('error')}")
    
    print("\n" + "=" * 50)
    
    # 分析图片 - 自定义问题
    print("\n2️⃣ 使用自定义问题分析:")
    custom_question = "请识别图片中是否有印章或签名，如果有请详细描述它们的特征和位置"
    result2 = analyze_image_with_qwen_vl(image_path, custom_question)
    
    if result2.get("success"):
        print(f"✅ 分析成功")
        print(f"❓ 问题: {result2['question']}")
        print(f"📝 回答: {result2['response']}")
        if result2.get("usage"):
            print(f"📊 Token使用情况: {result2['usage']}")
    else:
        print(f"❌ 分析失败: {result2.get('error')}")

def demo_multiple_questions():
    """演示对同一张图片问多个问题"""
    image_path = "image/2个章1个签名.jpg"
    
    if not os.path.exists(image_path):
        print(f"❌ 图片文件不存在: {image_path}")
        return
    
    questions = [
        "这张图片中有什么内容？",
        "请统计图片中印章和签名的数量",
        "请描述印章的颜色和形状特征",
        "请分析签名的位置和特点"
    ]
    
    print(f"🔍 对图片 {image_path} 进行多问题分析")
    print("=" * 60)
    
    for i, question in enumerate(questions, 1):
        print(f"\n📋 问题 {i}: {question}")
        print("-" * 40)
        
        result = analyze_image_with_qwen_vl(image_path, question)
        
        if result.get("success"):
            print(f"✅ 回答: {result['response']}")
        else:
            print(f"❌ 分析失败: {result.get('error')}")

if __name__ == "__main__":
    # 检查环境变量
    if not os.getenv("DASHSCOPE_API_KEY"):
        print("⚠️  警告：未设置 DASHSCOPE_API_KEY 环境变量")
        print("请先设置环境变量：")
        print("Windows: set DASHSCOPE_API_KEY=your_api_key")
        print("Linux/Mac: export DASHSCOPE_API_KEY=your_api_key")
        print("或者在代码中直接指定 api_key 参数")
        print()
        
        # 询问是否继续
        choice = input("是否继续运行演示？(y/n): ").lower().strip()
        if choice != 'y':
            print("程序退出")
            exit()
    
    print("🚀 视觉模型调用演示")
    print("=" * 60)
    
    # 运行单张图片分析演示
    demo_single_image()
    
    print("\n" + "=" * 60)
    
    # 运行多问题分析演示
    demo_multiple_questions()
    
    print("\n✨ 演示完成！")
