# 导入word，规则库




# -*- coding: utf-8 -*-
"""
@Des     : 检索图谱中的数据，插入到库， 作为A类问题
检索word中的数据，拆分，插入库库，作为B类问题
"""
import datetime
import json
import math
import re
import time
from uuid import uuid4
from docx import Document
import sys
import os

import requests
from requests.models import HTTPBasicAuth

root_path = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
sys.path.append(root_path)
from steps.tools import get_prompt,qwen7b,qwenMax

def getKeywords(content):
    for i in range(5):
        try:
            messages = [{
                "role": "user",
                "content": get_prompt("getkeywords2_template.txt", content, [])
            }]
            response = qwen7b(messages)
            return json.loads(response)["keywords"]
        except :
            continue

def createIndex(index_name):
    init_data = json.load(open("./RAG/schema.json",encoding="utf-8"))
    inteligentsearch_url = "http://192.168.186.65/inteligentsearch/rest"

    init_data["esdsid"] = 8
    init_data["category"]["categoryName"] = index_name
    init_data["category"]["categoryDescribe"] = index_name
    init_data["category"]["categoryNum"] = index_name

    init_data['category'] = json.dumps(init_data['category'])
        # {**(json.loads(init_data['category']) if isinstance(init_data['category'], str) else init_data['category']),
        #     'categoryNum': index_name}, ensure_ascii=False)
   
    build_url = inteligentsearch_url + "/esinteligentsearchmanageinit/addCategory"
    headers = {
        'Content-Type': 'application/json',
        "Authorization": "Bearer " + "token",
        "User-Agent": "python"
    }

    response = requests.post(url=build_url, json=init_data, headers=headers)
    print(response.text)

def embding(q_list):
    embed_url = "http://192.168.186.18:9081/linezhengshi_embedding/embed"
    data_embed = {"text": q_list}
    headers = {
        'Content-Type': 'application/json',
        "User-Agent": "python",
        "Authorization": "Bearer 4b1f2daed98265cc43aa7ebccfa89f0e",
    }
    response = requests.post(url=embed_url, data=json.dumps(data_embed), headers=headers)
    results = response.text
    return json.loads(results)["result"]

def insertData(question, index_name,path,graphid,keywords):
    es_url = "https://192.168.186.63:9200"
    id = str(uuid4())

    embed_results = embding([question])[0]
    if not keywords:
        keywords =""
    keywords_results = embding([keywords])[0]
    time_now = datetime.datetime.now().strftime('%Y-%m-%d')

    path_results = embding([path])[0]
    data = {
            "kworg_keyword":keywords,
            "kw_text":keywords,
            "kwvec_vector":keywords_results,

            "pathorg_keyword":path,
            "path_text":path,
            "pathvec_vector":path_results,

            "content_text":question,
            "contentvec_vector":embed_results,                    
            "id_keyword":id, "graphid_keyword":graphid,"infodate_date": time_now, "syscategory_keyword": index_name}
    data = json.dumps(data)


    # result = self.data_inseart(self.es_url, index_name, data, id, self.username, self.password)
    headers = {'Content-Type': 'application/json'}
    insert_url = es_url + "/" + index_name + "/_doc" + "/"
    response = requests.put(insert_url + id, headers=headers, auth=HTTPBasicAuth("admin", "admin"),
                            verify=False, data=data.encode('utf-8'))
    try:
        re = json.loads(response.text)
        if "error" in re:
            error = re["error"]["type"]
            print(re)
            print(f"[*] insert应用中，导入数据失败，请检查传参，错误类型{error}")
        else:
            # pass
            result = re["result"]
            print(f"[*] 导入数据成功，主键：{id}，状态：{result}")
    except Exception as e:
        re = e
        print(f"[*] insert应用中， 导入数据出错，请检查es地址是否访问通，错误类型{e}，es接口返回{response.text}")
        

def parse_txt_to_list(file_path):
    with open(file_path, 'r', encoding='utf-8') as file:
        lines = file.readlines()

    result = []
    current_chapter = None
    current_section = None
    section_content = []

    for line in lines:
        line = line.strip()
        # 匹配章
        chapter_match = re.match(r'第(.+?)章、(.+)', line)
        if chapter_match:
            if current_chapter and section_content:
                # 保存上一个节的内容
                result.append({
                    "path": f"{current_chapter}-{current_section}" if current_section else current_chapter,
                    "content": "\n".join(section_content)
                })
                section_content = []

            current_chapter = chapter_match.group(0)
            current_section = None  # 重置节

        # 匹配节
        section_match = re.match(r'第(.+?)节、(.+)', line)
        if section_match:
            if current_section and section_content:
                # 保存上一个节的内容
                result.append({
                    "path": f"{current_chapter}-{current_section}",
                    "content": "\n".join(section_content)
                })
                section_content = []

            current_section = section_match.group(0)

        # 匹配条或其他内容
        elif line:
            section_content.append(line)

    # 保存最后的内容
    if section_content:
        result.append({
            "path": f"{current_chapter}-{current_section}" if current_section else current_chapter,
            "content": "\n".join(section_content)
        })

    return result

def combinemerterial(lst):
    #将同名父章节的内容直接并入子章节中
    newdt = {}

    for item in lst:
        path = item["path"]
        newpath = re.sub(r'第[一二三四五六七八九十百千万\d]+章、|第[一二三四五六七八九十百千万\d]+节、', '', path)
        newdt[newpath] = item

    for item in lst:
        path = item["path"]
        newpath = re.sub(r'第[一二三四五六七八九十百千万\d]+章、|第[一二三四五六七八九十百千万\d]+节、', '', path)
        subpath = "-".join(newpath.split("-")[:-1])

        if subpath in newdt:
            parentcontent = newdt[subpath]["content"]
            item["content"] = parentcontent + "\n" + item["content"]

    return lst


if __name__ == "__main__":
    #  删除时，需要手动到ES平台中， 数据集成->统一元数据->元数据->元数据管理，搜索到了，删除，再到回收站，删除一下
    index_name= "nzyxzrgs19"
    createIndex(index_name)

    #将word转为txt再导入，好处是可以读到章节编号 
    doc_path = './data/深圳市市场主体登记业务规则20250108.txt'
    lst = parse_txt_to_list(doc_path)

    lst = combinemerterial(lst)

    for object in lst:
        path = object["path"]
        desc = object["content"]
        keywords = getKeywords(path + object["content"])
        # 数据导入
        result = insertData(desc, index_name,path,"",keywords)