

POST http://192.168.186.18:9002/linezhengshi_rag/retrieve
Content-Type: application/json

{
    "question": "股权出质登记 注销 新设 办理方式",  
    "isreranker": "true",  
    "pageSize": 5,  
    "retrieval_method": 1,  
    "select_condition":{"dbguid": "A72FB8C5-4AA0-494F-B00C-90CF29904D6E"}
}


####
POST http://192.168.219.30:8080/epoint-blbb-web/rest/dynamicapi/queryAll
Content-Type: application/json
Authorization: Bearer ********************************

{
    "question": "我司注册地址为深圳市福田区福田街道，为外商独资企业，现已经办结一笔股权出质登记，后续拟办理该笔股权出质登记的注销，并新设一笔股权出质登记。请问：深圳市的预约系统中，是否在注销登记完成的当天或之前即可办理新设登记的预约？",
    "keywords": "股权出质登记 注销 新设 办理方式",
    "top": 3
}

####
POST http://192.168.186.65/inteligentsearch/rest/esinteligentsearch/getByVector
Content-Type: application/json
Authorization: Bearer token
User-Agent: python

{
	"wd": "内资有限责任公司名称变更登记",
	"fields": "kw",
	"re_fields": "ptype;purpose;kw",
	"vecfield": "contentvec",
	"cnums": "nzyxzrgsv2_4",
	"pn": 0,
	"rn": 500,
	"vecType": "1",
	"pluginName": "EmbeddingOperatorNew",
	"isSynonym": "1",
	"esdsid": 8,
	"accuracy": 15,
	"rescore": "{'inteligentsearchType':'equalratio','sim1':'kworg'}",
	"opCondition": [
		{
			"conditionList": [
				{
					"ptype": "内资有限责任公司及分公司"
				}
			],
			"highlights": "false"
		},
		{
			"conditionList": [
				{
					"purpose": "变更"
				}
			],
			"highlights": "false"
		}
	],
	"opType": "0"
}

####
POST http://***************:8080/inteligentsearch/rest/esinteligentsearch/getSynonymAndNear
Content-Type: application/json
Authorization: Bearer token
User-Agent: python

{
    "wd": "内资有限责任公司 法人变更 股权变更 办理方式",
    "cnums": "nzyxzrgs27",
    "isParticiple": "0",
    "esdsid": 8
}


####
POST http://***************:8080/inteligentsearch/rest/esinteligentsearch/getFullTextDataNew
Content-Type: application/json
Authorization: Bearer token
User-Agent: python

{
        "isBusiness": 1,
        "cnum": "nzyxzrgsv2_2",
        "esdsid": "8",
        "pn": 200,
        "rn": 20,
}