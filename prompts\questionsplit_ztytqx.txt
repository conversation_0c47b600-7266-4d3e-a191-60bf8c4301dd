#目的#
请你作为语义分析专家和市场主体登记领域的业务专家，识别出最匹配的的主体类型、意图、情形

#名词解释#
市场主体登记领域：包含“公司、企业、个体户、个体工商户”等代表市场主体类型的内容，或者包含了“营业执照”等和市场主体登记密切相关的内容
主体类型：主体类型是指企业的类型划分‌，具体的分类标准有多种，包括但不限于有限责任公司、股份有限公司、合伙企业等。这些分类标准可以根据股东的责任形式、企业的组织形式等因素进行划分‌
意图：想要干的事情
情形：用户完成意图时自身的背景情况

#数据集定义#
1、主体类型集合：{entitytypelist}
2、意图集合：{intensionlist}
3、情形集合：{conditionlist}

#问题描述#
问题：{query}

#简称规则#
1、内资企业、有限责任公司、内资公司、内资有限公司是内资有限责任公司的简称
2、外资企业、外资公司是外资有限责任公司的简称

#分析过程#

1、根据意图的名词解释，结合市场主体登记领域政务服务相关行业知识，从问题描述分析出用户所有直接意图，直接意图必须是清晰明确的，如果问题描述中的意图不明确，尝试从问题描述用户当前遇到困难或困扰中推测他的直接意图，如确实分析不出直接意图，则直接意图设置为空。直接意图可以是0个、1个或者多个。同时说明从哪些句子或词语分析出的直接意图
2、结合结合政务服务相关知识，从意图集合中找出与直接意图最匹配的意图作为本次的匹配意图，匹配意图可以是0个、1个或者多个。需要说明最匹配的理由，同时用数字量化匹配度，数字范围0-100
3、根据主体类型的名词解释，从问题描述分析出可能的主体类型。可能的主体类型可以是0个、1个或者多个。问题描述涉及主体类型简称时参考简称分析。问题描述中没有明确的公司类型信息时不做假设。 同时说明从哪些句子或词语分析出的主体类型
4、从主体类型集合中找出最匹配的主体类型，可以是0个、1个或者多个。需要说明最匹配的理由,同时用数字量化匹配度，数字范围0-100
5、根据情形的名词解释，从问题描述分析出情形，情形可以是0个、1个或者多个。请详细说明分析出情形的过程，说明从哪些句子或词语分析出的情形
6、结合结合政务服务相关知识，从情形集合找出最匹配的情形，可以是0个、1个或者多个。需要说明最匹配的理由，同时用数字量化匹配度，数字范围0-100
7、请严格按照1-6步骤分析、处理，返回分析过程



#参考例子#
1、原始问题：有限责任公司变更住所，住所是租赁的需要提供什么材料？
分析：首先可以得出，问题的主体类型为有限责任公司，意图为变更住所，可以匹配上意图集合中的“住所/经营场所”，情形为住所是租赁也可以匹配上情形集合中的“住所/经营场所”。然后分别将得出的主体类型、意图、情形和数据集定义中的内容进行匹配。
最终得出答案
{
    "主体类型":"内资有限责任公司及分公司",
    "意图": "住所/经营场所",
    "情形": "住所/经营场所"
}

#返回格式#
按如下json格式返回结果，主体类型、意图、情形如识别多个，用逗号分开返回
        {
            "主体类型":"主体类型",
            "意图": "意图",
            "情形": "情形"
        }
    1、如果主体类型、情形和意图都没有提取出来，则返回空字符串：
        {
            "主体类型":"",
            "意图": "",
            "情形": ""
        }
    2、如果只有意图提取出来，则返回意图：如意图有读个，
        {
            "主体类型":"",
            "意图": "意图",
            "情形": ""
        }
    3、如果只有情形提取出来，则返回情形：
        {
            "主体类型":"",
            "意图": "",
            "情形": "情形"
        }
    4、如果主体类型、意图和情形都提取出来，则返回意图和情形：
        {
            "主体类型":"主体类型",
            "意图": "意图",
            "情形": "情形"
        }