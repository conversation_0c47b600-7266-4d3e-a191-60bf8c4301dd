tools = [
        {
            "name": "通用意图",
            "guid": "3a9d3767-a1f5-493b-a1ce-0d777212745a",
            "description": "",
            "type": "workflow",
            "parameters": {
                "type": "object",
                "properties": {}
            }
        },
        {
            "name": "问数统计",
            "guid": "47e60cdf-5197-42ce-a94c-11d56e6c6c53",
            "description": "用户围绕特定时间段、特定地区、特定版本等条件，询问关于某类数据的统计量（数量、同比、环比等），或数据在一定时间范围内的变化趋势。",
            "type": "workflow",
            "parameters": {
                "type": "object",
                "properties": {
                    "问题内容": {
                        "description": "content",
                        "type": "String"
                    }
                }
            }
        },
        {
            "name": "统计数据解读",
            "guid": "36fc9d15-a05f-4489-b662-e0e604c13782",
            "description": "",
            "type": "workflow",
            "parameters": {
                "type": "object",
                "properties": {
                    "问题内容": {
                        "description": "",
                        "type": "String"
                    }
                }
            }
        },
        {
            "name": "推荐相似问题",
            "guid": "39f1db1e-b412-4efa-a0bd-61c2e6dabb79",
            "description": "",
            "type": "workflow",
            "parameters": {
                "type": "object",
                "properties": {
                    "问题内容": {
                        "description": "",
                        "type": "String"
                    },
                    "消息标识": {
                        "description": "",
                        "type": "String"
                    }
                }
            }
        },
        {
            "name": "问数明细",
            "guid": "fb130996-f3c5-4111-9155-9f50fa489956",
            "description": "用户询问特定项目或事件的具体细节信息，问答助手会针对该问题给出详细且具体的回答，如项目的某个环节进展情况、涉及的具体数据等。",
            "type": "workflow",
            "parameters": {
                "type": "object",
                "properties": {
                    "问题内容": {
                        "description": "content",
                        "type": "String"
                    },
                    "问题记录标识": {
                        "description": "messageguid",
                        "type": "String"
                    },
                    "是否追问": {
                        "description": "isprobe",
                        "type": "String"
                    },
                    "追问的前次提问内容": {
                        "description": "previouscontnt",
                        "type": "String"
                    }
                }
            }
        },
        {
            "name": "问数专报",
            "guid": "854a0506-30e8-4cd5-acd6-105381ab7039",
            "description": "用户请求提供针对某一主题（如企业招标采购数据年度分析）的专门报告，通常这类报告涉及深入的分析和总结 。​",
            "type": "workflow",
            "parameters": {
                "type": "object",
                "properties": {
                    "问题内容": {
                        "description": "content",
                        "type": "String"
                    }
                }
            }
        },
        {
            "name": "问数报表",
            "guid": "2b09de2d-efa1-4beb-9d85-665a34235c57",
            "description": "用户直接索要某个特定时间的特定类型报表，希望获得包含相关数据及分析的完整报表呈现。",
            "type": "workflow",
            "parameters": {
                "type": "object",
                "properties": {
                    "问题内容": {
                        "description": "content",
                        "type": "String"
                    }
                }
            }
        },
        {
            "name": "关键词权重分析",
            "guid": "f0d0ee69-b3e3-48eb-ba24-e68d6df49501",
            "description": "",
            "type": "workflow",
            "parameters": {
                "type": "object",
                "properties": {
                    "数据表标识": {
                        "description": "datatableid",
                        "type": "String"
                    }
                }
            }
        },
        {
            "name": "消息记录新增更新接口",
            "guid": "999939",
            "description": "消息记录实体新增更新",
            "type": "api",
            "parameters": {
                "type": "object",
                "properties": {
                    "queryconditions": {
                        "description": "查询条件json",
                        "type": "String"
                    },
                    "question": {
                        "description": "问题名称",
                        "type": "String"
                    },
                    "queryresultjson": {
                        "description": "校验结果json",
                        "type": "String"
                    },
                    "rowguid": {
                        "description": "",
                        "type": "String"
                    },
                    "parsestarttime": {
                        "description": "",
                        "type": "String"
                    },
                    "parseendtime": {
                        "description": "",
                        "type": "String"
                    }
                }
            }
        },
        {
            "name": "思考过程接口",
            "guid": "getParsedS2SQLToJSON",
            "description": "思考过程",
            "type": "api",
            "parameters": {
                "type": "object",
                "properties": {
                    "question": {
                        "description": "",
                        "type": "String"
                    },
                    "appguid": {
                        "description": "",
                        "type": "String"
                    }
                }
            }
        },
        {
            "name": "问报告-专报条件核验接口（专报系统提供）",
            "guid": "99995",
            "description": "接口地址需要替换为专报系统地址。",
            "type": "api",
            "parameters": {
                "type": "object",
                "properties": {
                    "filtercondition": {
                        "description": "大模型回填的条件内容",
                        "type": "String"
                    },
                    "reportid": {
                        "description": "",
                        "type": "String"
                    }
                }
            }
        },
        {
            "name": "问报表-报表条件信息查询接口（报表系统提供）",
            "guid": "99991",
            "description": "接口地址需要替换为专报系统地址。",
            "type": "api",
            "parameters": {
                "type": "object",
                "properties": {
                    "reportguid": {
                        "description": "报表id",
                        "type": "String"
                    }
                }
            }
        },
        {
            "name": "问统计-sql语义召回接口",
            "guid": "getParsedS2SQL",
            "description": "http://***************:9081/epoint-AIChat-web\\n葛进：http://**************:8080/epoint-AIChat-web",
            "type": "api",
            "parameters": {
                "type": "object",
                "properties": {
                    "question": {
                        "description": "",
                        "type": "String"
                    },
                    "stream": {
                        "description": "",
                        "type": "String"
                    },
                    "appguid": {
                        "description": "",
                        "type": "String"
                    }
                }
            }
        },
        {
            "name": "问报告-专报报告查询接口（专报系统提供）",
            "guid": "9996",
            "description": "接口地址需要替换为专报系统地址。",
            "type": "api",
            "parameters": {
                "type": "object",
                "properties": {
                    "filtercondition": {
                        "description": "实际查询条件的json",
                        "type": "String"
                    },
                    "reportid": {
                        "description": "专报id",
                        "type": "String"
                    }
                }
            }
        }
    ]

toolnames = {"问数明细", "问数专报", "问数报表", "问数统计"}
resulttools = [item for item in tools if item.get("name") in toolnames]
print(resulttools)