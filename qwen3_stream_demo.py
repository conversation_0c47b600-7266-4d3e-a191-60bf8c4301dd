#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Qwen3大模型流式输出Demo
基于政务咨询场景的流式问答系统
"""

import json
import requests
import sys
import time
from typing import Iterator, Dict, Any
from dashscope import Generation
import random


class Qwen3StreamDemo:
    """Qwen3流式输出演示类"""
    
    def __init__(self):
        """初始化配置"""
        self.api_key = "sk-c342324d10474284abc452d71d0dd105"
        self.model = "qwen3-32b"
        
        # 政务咨询提示词模板
        self.system_prompt = """##职责 
你是一名资深政务顾问AI。你的核心使命仅仅结合我提供的"专用知识"回答用户的问题，不允许使用预训练的知识以及"专用知识"以外的任何知识。
你需要为用户提供关于政策法规、办事流程、民生服务等领域的权威、详尽且富有同理心的解答。
只允许根据"专用知识"字面内容进行回答，不允许联想、搜索

##要求
1、从现在起，你只能依据我提供给你的**我的知识**来回答问题。在回答任何问题时，不要使用你自身原本储备的任何其他知识来作答。请严格遵守这一规则。
2、答案中提到的地点、电话、材料、方式等，如果**我的知识**中有相关内容，以我的知识为准，并按原文知识逐条输出。
3、禁止在回复内容中列出知识条目！！！
4、回答中不得出现知识引用文本： "根据【我的知识】"、"参考知识库第X条"、"详见法规第X条"、 "根据知识库"、"根据知识"、"根据《xxx》第x条"、"根据xxx"、"（详见知识[x]）"、"（知识[x]）"、"（知识[x]第x条）"、"（第x条）"。请务必遵守此要求。
5、回答的章节内容需要逻辑清晰
6、要求输出简明扼要，不冗余
7、链接地址请严格按照格式输出！！！：江苏土地市场网（<a href="http://www.landjs.com" target="_blank" rel="noopener noreferrer">http://www.landjs.com</a>）
8、在回答的最后另起两行增加回复内容"以上内容由AI智能生成，可供参考。"。

##地址输出要求
1、线下地址需与"专用知识"原文地址信息完全一致；
2、如果"我的知识"中没有地址就不要输出；
3、如果"我的知识"中有地址并且需要通过地址信息回答用的问题，并按表格（区划、地址和联系电话）格式，全部输出，不允许缩略和让用户详见知识原文。
4、禁止回复"专用知识"以外的线下地址
5、答案中涉及办理地址的部分，需要按表格（区划、地址和联系电话）格式，全部输出，不允许缩略和让用户详见知识原文。

##我的知识
1. 缓缴养老保险费对员工退休待遇的影响：
职工办理退休前需缴清社会保险费，方可办理退休手续。基本养老金根据个人累计缴费年限、缴费工资、当地职工平均工资、个人账户金额、城镇人口平均预期寿命等因素确定。

2. 退休后指南：
一、企业职工及灵活就业人员办理退休
办理事项：企业职工基本养老保险退休审批
（一）、申请条件
需满足以下条件：
1.年龄条件：（1）正常退休年龄：从2025年1月1日起，男职工和原法定退休年龄为五十五周岁的女职工，法定退休年龄每四个月延迟一个月，分别逐步延迟至六十三周岁和五十八周岁；原法定退休年龄为五十周岁的女职工，法定退休年龄每二个月延迟一个月，逐步延迟至五十五周岁。国家另有规定的，从其规定；（2）弹性提前退休年龄：职工达到最低缴费年限，可以自愿选择弹性提前退休，提前时间最长不超过三年，且退休年龄不得低于女职工五十周岁、五十五周岁及男职工六十周岁的原法定退休年龄；（3）弹性延迟退休年龄：职工达到法定退休年龄，所在单位与职工协商一致的，可以弹性延迟退休，延迟时间最长不超过三年。国家另有规定的，从其规定。 
2.缴费条件：从2030年1月1日起，将职工按月领取基本养老金最低缴费年限由十五年逐步提高至二十年，每年提高六个月。 《江苏省企业职工基本养老保险规定》（省政府令第146号）第23条：参保人员依法参加基本养老保险，达到国家和省规定的退休年龄且缴费年限满足国家规定最低缴费年限的，按月领取基本养老金。《全国人民代表大会常务委员会关于实施渐进式延迟法定退休年龄的决定》、《国务院关于渐进式延迟法定退休年龄的办法》。
（二）、申请材料
1.居民身份证件/护照
2.社会保障卡
3.江苏省企业职工退休审批表
4.职工个人档案
5.一寸照片一张
6.企业职工基本养老保险退休时间申请书
7.退休办理需要的其他必要材料等
（三）、办理地址
线上办理地址：登录江苏省人力资源和社会保障厅网上办事服务大厅（<a href="https://rs.jshrss.jiangsu.gov.cn/index/" target="_blank" rel="noopener noreferrer">https://rs.jshrss.jiangsu.gov.cn/index/</a>），申请后线下审核。
线下办理地址：江苏省高邮市海潮东路997号 高邮市政务服务中心二楼D区人社退休一件事窗口，咨询电话：0514-84642027。
（四）、办理流程
1.线上办理流程：
登录江苏省人力资源和社会保障厅网上办事服务大厅（<a href="https://rs.jshrss.jiangsu.gov.cn/index/" target="_blank" rel="noopener noreferrer">https://rs.jshrss.jiangsu.gov.cn/index/</a>），选择企业职工基本养老保险退休申请事项后，下载并打印申请表，并按照业务办理的指南准备的相关业务材料，前往人社服务大厅现场取号审核办理。
2.线下办理流程：
申请人按照业务办理的指南准备业务的相关材料，前往人社服务大厅现场取号办理。
（五）、办理时限
法定办结时限：20个工作日
（六）、办理结果
企业职工退休审批表
（七）、收费标准
不收费。

四、退休后相关事宜
1、养老金发放时间：机关事业单位退休金每月10日左右发放；企业职工退休金每月15日左右发放。
2、养老金年度认证资格认证方式：可以通过江苏智慧人社APP或微信小程序进行待遇资格认证。
3、退休人员享受医保待遇缴费年限：医疗保险缴费年限要求因参保类型（职工医保或城乡居民医保）而异，参加职工基本医疗保险累计缴费年限（包含按照国家规定认可的视同缴费年限和实际缴费年限）男性满二十五年、女性满二十年；城乡居民医保无缴费年限要求，需要每年缴费才能享受医保待遇。
4、若您有公积金，退休后您可至公积金窗口咨询公积金余额提取事宜"""

    def create_messages(self, user_question: str) -> list:
        """创建消息列表"""
        return [
            {"role": "system", "content": self.system_prompt},
            {"role": "user", "content": user_question}
        ]

    def call_qwen3_stream(self, messages: list) -> Iterator[str]:
        """调用Qwen3模型进行流式输出"""
        try:
            seed = random.randint(1, 10000)
            response = Generation.call(
                model=self.model,
                messages=messages,
                api_key=self.api_key,
                seed=seed,
                result_format='message',
                temperature=0.3,
                enable_thinking=False,
                stream=True,  # 启用流式输出
                stop='Observation'
            )
            
            # 处理流式响应
            for chunk in response:
                if hasattr(chunk, 'output') and hasattr(chunk.output, 'choices'):
                    if chunk.output.choices and len(chunk.output.choices) > 0:
                        choice = chunk.output.choices[0]
                        if hasattr(choice, 'message') and hasattr(choice.message, 'content'):
                            content = choice.message.content
                            if content:
                                yield content
                        elif hasattr(choice, 'delta') and hasattr(choice.delta, 'content'):
                            content = choice.delta.content
                            if content:
                                yield content
                                
        except Exception as e:
            yield f"调用模型时发生错误: {str(e)}"

    def stream_response(self, user_question: str) -> None:
        """流式输出响应"""
        print(f"用户问题: {user_question}")
        print("=" * 50)
        print("AI回答:")
        
        messages = self.create_messages(user_question)
        
        try:
            full_response = ""
            for chunk in self.call_qwen3_stream(messages):
                print(chunk, end='', flush=True)
                full_response += chunk
                time.sleep(0.01)  # 模拟打字效果
            
            print("\n" + "=" * 50)
            print("回答完成")
            
        except Exception as e:
            print(f"\n发生错误: {str(e)}")

    def simple_call_qwen3(self, user_question: str) -> str:
        """简单调用Qwen3模型（非流式）"""
        messages = self.create_messages(user_question)
        
        try:
            seed = random.randint(1, 10000)
            response = Generation.call(
                model=self.model,
                messages=messages,
                api_key=self.api_key,
                seed=seed,
                result_format='message',
                temperature=0.3,
                enable_thinking=False,
                stream=False,
                stop='Observation'
            )
            
            return response.output.choices[0]['message']['content']
            
        except Exception as e:
            return f"调用模型时发生错误: {str(e)}"


def main():
    """主函数"""
    demo = Qwen3StreamDemo()
    
    # 测试问题
    test_question = "我老了怎么办"
    
    print("Qwen3大模型流式输出Demo")
    print("=" * 60)
    
    # 流式输出演示
    print("\n【流式输出演示】")
    demo.stream_response(test_question)
    
    print("\n\n【非流式输出演示】")
    print(f"用户问题: {test_question}")
    print("=" * 50)
    print("AI回答:")
    response = demo.simple_call_qwen3(test_question)
    print(response)
    print("=" * 50)
    print("回答完成")


if __name__ == "__main__":
    main()
