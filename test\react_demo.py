#!/usr/bin/env python3
# -*- coding: utf-8 -*-

def main(reAct_prompt, question, reAct_result, observation="") -> dict:
    """
    ReAct模式的主函数，处理多轮对话和推理
    
    Args:
        reAct_prompt: 当前的ReAct提示词，如果为空则初始化
        question: 用户问题
        reAct_result: 前一轮的ReAct结果
        observation: 最新的观察结果
    
    Returns:
        dict: 包含result字段的字典
    """
    
    # 初始化提示词模板
    initial_prompt = """你是一个可以推理和行动的问题拆解与回答助手，你可以使用以下工具：

统计(子问题):用户询问关于某类数据的统计量（数量、同比、环比、排名、占比等），或数据在一定时间范围内的变化趋势。
明细(子问题):用户询问特定项目或事件的具体细节信息，问答助手会针对该问题给出详细且具体的回答，如项目的某个环节进展情况、涉及的具体数据等。
报表(子问题):用户直接索要某个特定时间的特定类型报表，希望获得包含相关数据及分析的完整报表呈现。
专报(子问题):用户请求提供针对某一主题（如企业招标采购数据年度分析）的专门报告，通常这类报告涉及深入的分析和总结 。

用户历史咨询记录：

使用下面的格式进行推理和行动：

Question：你必须回答的输入问题
Thought：你应当始终思考接下来该怎么做。仔细分析历史咨询记录中是否已经包含了回答当前问题所需的信息，如果历史记录中已有相关数据，请直接利用这些信息进行推理，避免重复查询已知信息。
Action：要执行的操作，应当是统计、明细、报表、专报之一
Action Input：该操作的输入，也就是子问题，子问题格式要求：
 - 子问题尽量一句话描述，不要断句，仅包含一个问题。
 - 子问题尽量书面化，规范化，去除俗语，尽量可以转化为标准的指标信息，比如（数量、占比、排名等）。
Observation：该操作的执行结果。action未执行成功会返回"意图解析失败"（这种情况下，你可以跳过当前问题直接进入下一个问题，如果是最后一个问题或者下一个问题依赖当前问题结果直接结束）。
...（可以重复上述 Thought / Action / Action Input / Observation 这一组零次或多次）
Thought：我现在知道最终答案了
Final Answer：对原始输入问题的最终回答

示例flewshot:
Question：2024年进场的项目数量是多少？同比是多少？

Thought：用户的问题包含两个子问题："2024年进场的项目数量是多少？"和"同比是多少？"。目前历史咨询记录中没有关于2024年进场项目数量的信息，因此需要执行统计操作来获取这些数据。
Action：统计
Action Input：2024年进场的项目数量是多少？

Observation：2024年进场的项目数量 100

Thought：第一个问题答案是2024年进场项目数量为100个，我再确认第二个问题，结论和历史咨询记录中均未给出相关信息，因此需要询问具体同比信息。
Action：统计
Action Input：2024年进场项目数量同比是多少

Observation：2024年进场的项目数量同比 52%

Thought：我现在知道最终答案了  
Final Answer：2024年进场的项目数量是100个，同比为52%。

开始！

"""
    
    # 如果reAct_prompt为空，初始化提示词
    if not reAct_prompt:
        prompt = initial_prompt + f"Question：{question}\n"
    else:
        # 拼接前一次的结果和新的观察
        prompt = reAct_prompt
        if reAct_result:
            prompt += reAct_result + "\n"
        if observation:
            prompt += f"Observation：{observation}\n"
    
    return {
        'result': prompt
    }


# 演示使用示例
def demo():
    print("=== ReAct Demo ===\n")
    
    # 第一轮：初始化
    print("第一轮调用 - 初始化提示词")
    result1 = main("", "2024年进场的项目数量是多少？同比是多少？", "", "")
    print("返回的prompt长度:", len(result1['result']))
    print("prompt结尾部分:")
    print(result1['result'][-200:])
    print("\n" + "="*50 + "\n")
    
    # 第二轮：添加第一次推理结果
    print("第二轮调用 - 添加第一次推理结果")
    react_result1 = """Thought：用户的问题包含两个子问题："2024年进场的项目数量是多少？"和"同比是多少？"。目前历史咨询记录中没有关于2024年进场项目数量的信息，因此需要执行统计操作来获取这些数据。
Action：统计
Action Input：2024年进场的项目数量是多少？"""
    
    observation1 = "2024年进场的项目数量 100"
    
    result2 = main(result1['result'], "", react_result1, observation1)
    print("返回的prompt长度:", len(result2['result']))
    print("prompt结尾部分:")
    print(result2['result'][-300:])
    print("\n" + "="*50 + "\n")
    
    # 第三轮：继续推理
    print("第三轮调用 - 继续推理第二个问题")
    react_result2 = """Thought：第一个问题答案是2024年进场项目数量为100个，我再确认第二个问题，结论和历史咨询记录中均未给出相关信息，因此需要询问具体同比信息。
Action：统计
Action Input：2024年进场项目数量同比是多少"""
    
    observation2 = "2024年进场的项目数量同比 52%"
    
    result3 = main(result2['result'], "", react_result2, observation2)
    print("返回的prompt长度:", len(result3['result']))
    print("prompt结尾部分:")
    print(result3['result'][-400:])
    print("\n" + "="*50 + "\n")
    
    # 第四轮：最终答案
    print("第四轮调用 - 生成最终答案")
    react_result3 = """Thought：我现在知道最终答案了
Final Answer：2024年进场的项目数量是100个，同比为52%。"""
    
    result4 = main(result3['result'], "", react_result3, "")
    print("最终完整的ReAct对话:")
    print(result4['result'][-600:])


if __name__ == "__main__":
    demo()
