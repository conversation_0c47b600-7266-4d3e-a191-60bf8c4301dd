import base64
import csv
import json
import traceback

import requests

file_path = "三级节点合并图谱.txt"
with open(file_path, "rb") as f:
    file_stream = f.read()
file_b64 = base64.b64encode(file_stream).decode("utf-8")

with open(file_path, "r", encoding="utf-8") as f:
    file_content = f.read()
fileContent = json.loads(file_content)

idMap = {}
idNameMap = {}
for entity in fileContent["entities"]:
    if "relations" in entity:
        for relation in entity["relations"]:
            idMap[relation["target"]] = entity["id"]
    idNameMap[entity["id"]] = entity["name"]


def a_question_qw(question, isToPath=False):
    url = "http://**************:8080/llm/predict"
    stream = False
    prompt = f"""请分析以下JSON结构的数据，重点理解其整体组织形式以及其中的实体（entities）与关系（relations）的逻辑。
每个实体包含以下关键属性：name（名称）、description（画像）、id（唯一标识符）和与其他实体的relations（关系）。
请按以下要求回答问题：
1. 任何时候都严格禁止查询任何联网信息，禁止调用大模型自己已有的知识储备。
2. 仔细分析提问内容，结合description和name找到与提问最相关的唯一实体，并返回实体节点的id。你必须逐级搜索实体，比如与一级实体相关，才能根据relations搜索相关的二级实体，依此类推。
3. 禁止对问题描述的情形做任何推测。
4. 如果用户的输入只体现了办事意愿但没有体现明确的问题时，请回复对应事项实体下的办理流程的节点id。
5. 如果问题中没有明确是什么类型的公司，默认为有限责任公司。
6. 以JSON的方式输出，仅包含一个实体节点id。如果不包含任何实体节点，id值返回空。不要输出JSON以外的内容。请勿给出分析过程。
7. 例如：{{"id": "116547166ac8439c"}}
8. 如果用户明确询问“流程如何”、“怎么办”、“如何办理”等涉及流程的问题，必须返回对应的办理流程节点ID，而不是直接返回相关实体的ID。
9. 对于不确定的情况，优先选择与用户问题最接近的实体，并返回其办理流程节点ID。
10. 确保严格按照用户问题的意图进行匹配，避免过度推测或简化问题。
11. 当用户询问“在哪里办理”或“办理地点”时，返回对应的“办理方式和办理地点”节点ID。
12. 当用户询问“需要准备哪些材料”时，返回对应的“申请材料”节点ID。
13. 当用户询问“流程如何”、“怎么办”、“如何办理”等涉及流程的问题时，返回对应的“办理流程”节点ID。
14. 当用户同时询问多个问题（如“在哪里办理”和“流程如何”），优先返回与主要问题最相关的节点ID。
示例问题及回答
示例1
提问：我司需要变更工商登记上法人证件号码，流程如何？
回答：{{"id": "8ff3f358691942f5"}}
示例2
提问：我们想开设一家内资有限责任公司，需要准备哪些材料？
回答：{{"id": "23995894a4a349a0"}}
示例3
提问：我想了解一下内资有限责任公司的注销手续。
回答：{{"id": "a322837776874e42"}}
提问：拟将注册资本由500万减至10万，请问网上填写减资公告在哪里办理？
回答：{{"id": "79678c36fabc4628"}}
提问：{question}"""

    prompttoPath = f"""请分析以下JSON结构的数据，重点理解其整体组织形式以及其中的实体（entities）与关系（relations）的逻辑。
每个实体包含以下关键属性：name（名称）、description（画像）、id（唯一标识符）和与其他实体的relations（关系）。
请按以下要求回答问题：
1. 任何时候都严格禁止查询任何联网信息，禁止调用大模型自己已有的知识储备。
2. 仔细分析提问内容，结合description和name找到与提问最相关的唯一实体，并返回实体的全路径。你必须逐级搜索实体，比如与一级实体相关，才能根据relations搜索相关的二级实体，依此类推。
3. 禁止对问题描述的情形做任何推测。
4. 如果用户的输入只体现了办事意愿但没有体现明确的问题时，请回复对应事项实体下的办理流程的节点。
5. 如果问题中没有明确是什么类型的公司，默认为有限责任公司。
6. 以JSON的方式输出，仅包含一个实体节点路径path。如果不包含任何实体节点，path返回空。不要输出JSON以外的内容。请勿给出分析过程。
7. 例如：{{"path": "备案-内资有限责任公司经营范围变动备案-办理时限"}}
8. 如果用户明确询问“流程如何”、“怎么办”、“如何办理”等涉及流程的问题，必须返回对应的办理流程节点，而不是直接返回相关实体。
9. 对于不确定的情况，优先选择与用户问题最接近的实体，并返回其办理流程节点。
10. 确保严格按照用户问题的意图进行匹配，避免过度推测或简化问题。
11. 当用户询问“在哪里办理”或“办理地点”时，返回对应的“办理方式和办理地点”节点。
12. 当用户询问“需要准备哪些材料”时，返回对应的“申请材料”节点。
13. 当用户询问“流程如何”、“怎么办”、“如何办理”等涉及流程的问题时，返回对应的“办理流程”节点。
14. 当用户同时询问多个问题（如“在哪里办理”和“流程如何”），优先返回与主要问题最相关的节点。
提问：{question}"""

    if isToPath:
        prompt = prompttoPath
    # 首次调用传入文件和自定义的cache_tag
    messages = [
        {"role": "user", "content": prompt}
    ]
    # print(prompt)

    data = {
        "messages": messages,
        "files": [file_b64],  # 文件可以是多个
        "stream": False,
    }

    response = requests.request("POST", url, data=json.dumps(data), stream=stream)
    return json.loads(response.text)['result']


with open('newquestion.csv', mode='r', newline='', encoding='utf-8') as file:
    reader = csv.reader(file)

    # 存储第一列和第二列的数据
    first_column = []
    second_column = []

    # 读取数据，先读取第一列
    for row in reader:
        if row and row[0]:  # 如果行不为空
            first_column.append(row[0])  # 添加第一列数据

    # 重新打开文件以便再次读取
    # file.seek(0)  # 重置文件指针到文件开头
    # for row in reader:
    #     if row and row[1]:  # 如果行不为空
    #         if len(row) > 1:  # 确保第二列存在
    #             second_column.append(row[1])  # 添加第二列数据
# suffix = "id"
# isToPath = True
# if isToPath:
#     suffix = "path"
# with open(file_path.replace(".txt", "测试结果" + suffix + ".csv"), mode='w', newline='', encoding='utf-8') as file:
#     writer = csv.writer(file)
#     # 遍历 first_column，调用处理方法并将返回的值写入新CSV
#     i = 1
#     for value in first_column:
#         result = a_question_qw(value, isToPath)  # 调用处理方法，得到一个值
#         # print(result)
#         print(i)
#         i += 1
#         names = ''
#         try:
#             content = result[result.index("{"):result.rindex("}") + 1]
#             array = json.loads(content)
#             if isToPath:
#                 if "path" in array:
#                     names = array["path"]
#             else:
#                 if "id" in array:
#                     id = array["id"]
#                     if id in idNameMap:
#                         names += idNameMap[id]
#                     else:
#                         names += 'id不在图谱中'
#                     parentId = id
#                     while parentId in idMap:
#                         parentId = idMap[parentId]
#                         names += "-" + idNameMap[parentId]
#         except:
#             print(result, traceback.format_exc())
#             names = '解析错误'
#         writer.writerow([value, result, names])  # 将返回的值作为单独的一列写入新CSV文件

result = a_question_qw("怎么线下预约办理内资有限责任公司备案（变更经营范围）找不到线下预约窗口，及具体流程")
print(result)
# content = result[result.index("{"):result.rindex("}")+1]
# id = json.loads(content)["id"]
# if id:
#     names = idNameMap[id]
#     if id in idMap:
#         if id in idMap:
#             parentId = idMap[id]
#             names += "-" + idNameMap[parentId]
#             if parentId in idMap:
#                 names += "-" + idNameMap[idMap[parentId]]
#     print(id, names)


