#检索图谱
import openpyxl

import requests
import json
import sys
import os

from openai import OpenAI

root_path = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
sys.path.append(root_path)
from steps.tools import get_prompt,qwen7b,qwenMax,callKimi

from combine.ch5 import callKimi
from tqdm import tqdm

def getKeywords(centense):
    messages = [ {
        "role": "user",
        "content": get_prompt("getkeywords_template.txt",centense,[])
    }]
    for i in range(3):
        try:
            response = qwenMax(messages)

            if "{" in response and "关键词" in response:
                response = json.loads(response)["关键词"]

            return response
        except Exception as e:
            continue

client = None

def callKimi(messages) -> str:

    global client
    if client is None:
        client = OpenAI(
            api_key = "sk-NOOv7U6XgWdCwLmkqVbnC14YIJjvzR1ZQNWezNJEC9L29j3S", # 在这里将 MOONSHOT_API_KEY 替换为你从 Kimi 开放平台申请的 API Key
            base_url = "https://api.moonshot.cn/v1",
        )
    # 我们将用户最新的问题构造成一个 message（role=user），并添加到 messages 的尾部
    # messages.append({
    #     "role": "user",
    #     "content": input,
    # })

    # 携带 messages 与 Kimi 大模型对话
    completion = client.chat.completions.create(
        model="moonshot-v1-8k",
        messages=messages,
        temperature=0,
    )

    # 通过 API 我们获得了 Kimi 大模型给予我们的回复消息（role=assistant）
    assistant_message = completion.choices[0].message

    # 为了让 Kimi 大模型拥有完整的记忆，我们必须将 Kimi 大模型返回给我们的消息也添加到 messages 中
    messages.append(assistant_message)

    return assistant_message.content


def callES(question,index_name,select_field,fields,num):
    inteligentsearch_url = "http://**************/inteligentsearch/rest"
    params = {
        "wd": question,
        "fields": select_field,
        "re_fields": fields,
        "vecfield": "contentvec",
        "cnums": index_name,
        "pn": 0,
        "rn": num,
        "vecType": "1",
        "pluginName": "EmbeddingOperatorNew",
        "isSynonym": "1",
        "esdsid": 8,
        "accuracy": 15,
        "opCondition": None,
        "opType": "0"
    }

    headers = {
        'Content-Type': 'application/json',
        "Authorization": "Bearer " + "token",
        "User-Agent": "python"
    }
    response = requests.post(url=inteligentsearch_url + "/esinteligentsearch/getByVector",
                                data=json.dumps(params), headers=headers)
    re = json.loads(response.text)
    results = eval(json.dumps(json.loads(re["content"])["result"]["records"]))
    return results

def get_results(data):
    reranker_url = "http://192.168.186.112:8080/epoint-gateway/linezhengshi_reranker/predict"
    headers = {
        'Content-Type': 'application/json',
        "User-Agent": "python",
        "Authorization": "Bearer 4b1f2daed98265cc43aa7ebccfa89f0e",
    }
    response = requests.post(url=reranker_url, data=json.dumps(data), headers=headers, verify=False)

    results = response.text

    return json.loads(results)

def getAllKW(keywords):
    reranker_url = "http://**************/inteligentsearch/rest/esinteligentsearch/getSynonymAndNear"
    headers = {
        'Content-Type': 'application/json',
        "User-Agent": "python",
        "Authorization": "Bearer 4b1f2daed98265cc43aa7ebccfa89f0e",
    }
    data ={
        "wd": keywords,
        "cnums": "nzyxzrgs27",
        "isParticiple": "0",
        "esdsid": 8
    }
    response = requests.post(url=reranker_url, data=json.dumps(data), headers=headers, verify=False)

    results = response.text

    dt = json.loads(json.loads(results)["content"])["result"]["tongyici"]

    all = []
    all = all +  dt.split(',')
    all = all +  keywords.split(' ')
    unique_list = list(dict.fromkeys(all))
    return " ".join(unique_list)


def query(question,keywords):

    allkw = getAllKW(keywords)

    #检索拓展知识（检索 172上的知识库，从那里上传,  名为：政务拓展知识)
    url = "http://**************:9002/linezhengshi_rag/retrieve"

    payload = {
        "question":question + " " + allkw,  
        "isreranker": False,  
        "pageSize": 30,  
        "retrieval_method": 4,  
	    "index_name": "blbb_1",
        "select_condition":{"classname": "问答对"}
    }
    headers = {
        'user-agent': "vscode-restclient",
        'content-type': "application/json"
    }

    response = requests.request("POST", url, data=json.dumps(payload), headers=headers)

    final_resultsEX = []
    for  item in response.json()["result"]["retrieve_result"]:
        final_resultsEX.append(item["content"]["question"] + "\r\n" + item["content"]["answer"])

    content =  final_resultsEX
    content = list(dict.fromkeys(content))

    data_reranker = {"text": question + allkw, "compare_list": content}
    reranker_results = get_results(data_reranker)

    c =""
    for item in reranker_results["result"][:10]:
        c = c + item[0][:60].replace("\r\n"," ").replace("\n"," ") + "\r\n"
    return c

    # content =""
    # for item in reranker_results["result"][:3]:
    #     content += item[0] + "\n"
    # return content

def query2(question,keywords):

     #A类问题，图谱入的ES
    index_name = "nzyxzrgsv2_x2"  #库名，需要变
    result_retrieve1 = callES(keywords, index_name,"content","content",30)

    lst = result_retrieve1

    allkw = getAllKW(keywords)
    
    text_list = [pre["content"] for pre in lst]
    data_reranker = {"text": question + allkw, "compare_list": text_list}
    reranker_results = get_results(data_reranker)
    results_reranker = reranker_results["result"]
    final_resultsA = []
    for i, re_reranker in enumerate(results_reranker):
        content, score = re_reranker[0], re_reranker[1]
        final_resultsA.append(content)

    c =""
    for item in final_resultsA[:10]:
        c = c + item[:60].replace("\r\n"," ").replace("\n"," ") + "\r\n"
    return c

    # content =""
    # for item in reranker_results["result"][:3]:
    #     content += item[0] + "\n"
    # return content

#注意，修改上面逻辑后，需同步修改线上的groovy脚本，以供给agent使用
#接口地址：http://192.168.219.30:8080/epoint-blbb-web/frame/pages/apimanage/apiinfo/apimanageinfotab?rowguid=queryAll&serviceid=e35be9f5-0b7b-448a-80f3-a7453381fe21&apitype=FUNCTIONAPI&appguid=null&tableId=queryAll&type=undefined
#test.rest 中可验证测试

def queryTest(question,keywords):

    url = "http://192.168.219.30:8080/epoint-blbb-web/rest/dynamicapi/queryX"

    payload =  {
            "question": question,
            "keywords":keywords,
            "top": 10
        }

    headers = {
        'user-agent': "vscode-restclient",
        'content-type': "application/json",
        'authorization': "Bearer 1ea82c5ae51f46bd41bd8b5224628fcd"
        }

    response = requests.request("POST", url, data=json.dumps(payload), headers=headers)

    return response.json()["dbginfo"]


if __name__ == '__main__':

    question = "问食品生产许可证网上申报系统出现问题，应联系哪个部门处理？"
    keywords = getKeywords(question) #+ "  增补换照"
    print(keywords)
    # keywords = "企业迁移 内资有限"
    # print(keywords)
    # keywords = "内资有限责任公司 备案 监事"
    # print(keywords)
    # content = query2(question,keywords)
    content = queryTest(question,keywords)

    print(content)

