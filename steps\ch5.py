### 调用kimmy 图谱检索示例

import time
import json
import requests
import base64


url = "http://192.168.186.18:8088/llm/predict"


#读取图谱文件
file_path = r"./data/律师执业注册知识图谱和规则图谱.txt"
with open(file_path, "rb") as f:
    file_stream = f.read()
file_b64 = base64.b64encode(file_stream).decode("utf-8")


cache_tag = "tempepointdt"  # 自定一个cache_tag

#上传文件
# # 首次调用传入文件和自定义的cache_tag
# messages = [
#     {
#         "role": "system",
#         "content": "你是 Kimi，由 Moonshot AI 提供的人工智能助手，你更擅长中文和英文的对话。你会为用户提供安全，有帮助，准确的回答。同时，你会拒绝一切涉及恐怖主义，种族歧视，黄色暴力等问题的回答。Moonshot AI 为专有名词，不可翻译成其他语言。",
#     },
#     {"role": "user", "content": "请简单介绍 律师执业注册知识图谱和规则图谱.txt 讲了啥"},
# ]

# data = {
#     "messages": messages,
#     "files": [file_b64],   # 文件可以是多个
#     "cache_tag": cache_tag,
#     "stream": stream
# }
# response = requests.request("POST", url, data=json.dumps(data))

# print(json.loads(response.text))

# # # 第二次直接传cache_tag，不传入files
# time.sleep(5)  # 等待5s，防止request过于频繁

#数据检索 
messages = [
    {
        "role": "system",
        "content": "请不要查询任何联网信息，也不需要调用大模型自己已有的知识储备，也不能对问题描述的情形做任何推测，仅根据刚才上传的知识图谱json文件提供的信息回答下面的问题。如果问题涉及到利用规则进行逻辑判断，请调用上传的知识图谱json文件中的需遵守的规则作为判断规则。回答问题时请直接给出答案，无需给出分析过程。如果我的提问包含多个问题，请用“一、二、三”这样的序号标明答案；如果只包含一个问题，则不要用序号标明答案。另外检索图库的关键字也返回一下。开始提问：",
    },
    {"role": "user", "content": "您好！我是广西户籍，法学本科生，2020年通过法考，2022年取得中国律师执业证，2024年12月注销了律师证，现在公司做法务。我的配偶是香港永久居民，我现在拟通过结婚方式赴港定居取得香港身份。请问，如果我取得了香港身份证，我还可以在深圳申请恢复律师执业吗？"},
]

data = {
    "messages": messages,
    "cache_tag": cache_tag,
    "stream": False
}

response = requests.request("POST", url, data=json.dumps(data))

print(json.loads(response.text)["result"])
