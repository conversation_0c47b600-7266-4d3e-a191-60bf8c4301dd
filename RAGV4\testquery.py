#导库，将原先的导入到新库中

import requests
import json
from requests.auth import HTTPBasicAuth
import urllib3

# 关闭 HTTPS 安全警告
urllib3.disable_warnings(urllib3.exceptions.InsecureRequestWarning)

def tranpage(pagenum):
    # Elasticsearch 查询接口
    search_url = "https://192.168.186.63:9200/zntp/_search"
    search_headers = {
        "Content-Type": "application/json"
    }
    auth = HTTPBasicAuth('admin', 'admin')  # Basic Auth

    search_body = {
        "from": pagenum*20,
        "size": 20,
        "query": {
            "match_all": {}
        },
        "highlight": {
            "pre_tags": [
                "<em style='color:red'>"
            ],
            "post_tags": [
                "</em>"
            ],
            "require_field_match": True,
            "fields": {
                "title_ngram": {}
            }
        }
    }

    # 关闭证书验证，仅用于测试环境
    response = requests.post(search_url, headers=search_headers, auth=auth, json=search_body, verify=False)
    hits = response.json().get("hits", {}).get("hits", [])

    # 插入接口地址
    insert_url = "http://localhost:9060/rag/embed"
    insert_headers = {
        "Content-Type": "application/json"
    }
    index_name = "d83f7707-7122-4095-9b3b-922f9844bd34"

    contentinfo_list = []
    for i, hit in enumerate(hits):
        source = hit.get("_source", {})

        print(source.get("domainname_keyword", ""))


for i in range(1,330):
    tranpage(i)
    print("finised page",i)