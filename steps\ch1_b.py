## 大模型提取关键字

import requests
import json
from tools import get_prompt,qwen7b,qwenMax,qwen14b

url ="http://192.168.232.20:9018/vllm/predict_async"



graph ="""(1)内资有限责任公司-注销-内资有限责任公司一般注销登记<br>-办理地点
(2)内资有限责任公司-注销-内资有限责任公司一般注销登记<br>-申请方式
(3)内资有限责任公司-注销-内资有限责任公司一般注销登记<br>-办理流程
(4)内资有限责任公司-注销分支机构-内资分公司一般注销登记-办理流程
(5)内资有限责任公司-注销分支机构-内资分公司一般注销登记-办理地点
(6)内资有限责任公司-注销分支机构-内资分公司一般注销登记-申请方式
(7)内资有限责任公司-注销-内资有限责任公司一般注销登记<br>-申请条件
(8)内资有限责任公司-注销-内资有限责任公司一般注销登记<br>-申请材料
(9)内资有限责任公司-注销-内资有限责任公司一般注销登记<br>-办理时限
(10)内资有限责任公司-注销-内资有限责任公司一般注销登记<br>-审批结果
(11)内资有限责任公司-注销-内资有限责任公司简易注销登记-办理地点
(12)内资有限责任公司-注销分支机构-内资分公司一般注销登记-申请材料
(13)内资有限责任公司-注销-内资有限责任公司简易注销登记-申请条件
(14)内资有限责任公司-注销分支机构-内资分公司一般注销登记-申请条件
(15)内资有限责任公司-注销分支机构-内资分公司简易注销登记-审批结果
"""

messages = [ {
    "role": "user",
    "content": get_prompt("routeChoise_template.txt","内资企业怎么注销？",[]).replace("graphcontent",graph)
},{
    "role": "assistant",
    "content": "您是要注销内资有限责任公司还是内资分公司？是选择一般注销登记还是简易注销登记？"
},{
    "role": "user",
    "content": get_prompt("routeChoise_template.txt","内资企业怎么注销？",[]).replace("graphcontent",graph)
}
]

response = qwen14b(messages)

print(response)
