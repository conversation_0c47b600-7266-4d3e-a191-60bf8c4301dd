##根据ES检索结果，批量刷记录
import openpyxl

import requests
import json
import sys
import os

root_path = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
sys.path.append(root_path)
from steps.tools import get_prompt,qwen7b,qwenMax

# from ch11 import queryB
# from ch12 import queryA
from combine.ch5 import callKimi
from tqdm import tqdm


url ="http://192.168.232.20:9018/vllm/predict_async"

def getKeywords(centense):
    messages = [ {
        "role": "user",
        "content": get_prompt("getkeywords_template.txt",centense,[])
    }]
    for i in range(3):
        try:
            response = qwen7b(messages)

            if "{" in response and "关键词" in response:
                response = json.loads(response)["关键词"]

            return response
        except Exception as e:
            continue

def checkAB(query):
    messages = [ {
        "role": "user",
        "content": get_prompt("checkAB_template.txt",query,[])
        }
    ]

    response = qwen7b(messages)
    return response


def answerbyGraph(query,graphcontent,chat_history):
    prompt = get_prompt("answer2_template.txt",query,[])
    prompt = prompt.replace("{graphcontent}", graphcontent)
    response = callKimi(prompt)

        
    # messages = [{
    #     "role": "user",
    #     "content": prompt
    # }
    # ]
    # response = qwenMax(messages)

    return response



def queryA(question,keywords):
    
    url = "http://192.168.219.30:8080/epoint-blbb-web/rest/dynamicapi/queryA"
    headers = {
        'Content-Type': 'application/json',
        "User-Agent": "python",
        "Authorization": "Bearer 4b1f2daed98265cc43aa7ebccfa89f0e",
    }
    data ={"question":question,"keywords":keywords,"top":"10"}

    response = requests.post(url=url, data=json.dumps(data), headers=headers, verify=False)
    dt = json.loads(response.text)

    content =""
    for item in dt["content"]:
        content += item["path"] + "\r\n" + item["content"] + "\n"

    return content


def queryB(question,keywords):
    
    url = "http://192.168.219.30:8080/epoint-blbb-web/rest/dynamicapi/queryB"
    headers = {
        'Content-Type': 'application/json',
        "User-Agent": "python",
        "Authorization": "Bearer 4b1f2daed98265cc43aa7ebccfa89f0e",
    }
    data ={"question":question,"keywords":keywords,"top":"3"}

    response = requests.post(url=url, data=json.dumps(data), headers=headers, verify=False)
    dt = json.loads(response.text)

    return dt["result"]


def get_results(data):
    reranker_url = "http://192.168.186.112:8080/epoint-gateway/linezhengshi_reranker/predict"
    headers = {
        'Content-Type': 'application/json',
        "User-Agent": "python",
        "Authorization": "Bearer 4b1f2daed98265cc43aa7ebccfa89f0e",
    }
    response = requests.post(url=reranker_url, data=json.dumps(data), headers=headers, verify=False)

    results = response.text

    return json.loads(results)
    
def queryAB(question,keywords):
    url = "http://192.168.219.30:8080/epoint-blbb-web/rest/dynamicapi/queryA"
    headers = {
        'Content-Type': 'application/json',
        "User-Agent": "python",
        "Authorization": "Bearer 4b1f2daed98265cc43aa7ebccfa89f0e",
    }
    data ={"question":question,"keywords":keywords,"top":"10"}

    response = requests.post(url=url, data=json.dumps(data), headers=headers, verify=False)
    dt = json.loads(response.text)

    content = []
    for item in dt["content"]:
        content.append(item["path"] + "\r\n" + item["content"])


    
    url = "http://192.168.219.30:8080/epoint-blbb-web/rest/dynamicapi/queryB"
    headers = {
        'Content-Type': 'application/json',
        "User-Agent": "python",
        "Authorization": "Bearer 4b1f2daed98265cc43aa7ebccfa89f0e",
    }
    data ={"question":question,"keywords":keywords,"top":"10"}

    response = requests.post(url=url, data=json.dumps(data), headers=headers, verify=False)
    dt = json.loads(response.text)
    for item in dt["lst"]:
        content.append(item)
    
    data_reranker = {"text": question, "compare_list": content}
    reranker_results = get_results(data_reranker)

    content =""
    for item in reranker_results["result"][:3]:
        content += item[0] + "\n"
    return content

def queryAll(question,keywords):

    url = "http://192.168.219.30:8080/epoint-blbb-web/rest/dynamicapi/queryAll"

    payload =  {
            "question": question,
            "keywords":keywords,
            "top": 3
        }

    headers = {
        'user-agent': "vscode-restclient",
        'content-type': "application/json",
        'authorization': "Bearer 1ea82c5ae51f46bd41bd8b5224628fcd"
        }

    response = requests.request("POST", url, data=json.dumps(payload), headers=headers)

    return response.json()['result']

def ScanData():
    # 打开xlsx文件
    wb = openpyxl.load_workbook('../result/testdtv6.xlsx')
    sheet = wb.active

    # 假设数据从第 2 行开始
    rows = sheet.rows
    for row in tqdm(rows, desc="Processing", ncols=100): #
        if len(row) < 2:
            continue
        col1_value = row[1].value  # 获取第一列的值
        if col1_value =="原始问题":
            continue
        if col1_value == None:
            continue
        # col2_value = row[2].value  # 获取第二列的值
        keywords = getKeywords(col1_value)
        # tp = checkAB(col1_value)
        # if "A" in tp:
        #     content = queryA(col1_value,keywords)
        # else:
        #     content = queryB(col1_value,keywords)
        content = queryAll(col1_value,keywords)

        ret =""
        # try:
        #     ret = answerbyGraph(col1_value,content,[])
        # except Exception as e:
        #     print(e)
        #     print(len(content))

            
        messages = [ {
            "role": "user",
            "content": get_prompt("chekanswer_template.txt",col1_value,[]).replace("{answer}",content)
        }
        ]
        ret = qwenMax(messages)

        
        # 在第三列写入 'Hello'
        # sheet.cell(row=row[0].row, column=3, value=tp)
        sheet.cell(row=row[0].row, column=6, value=keywords)
        sheet.cell(row=row[0].row, column=7, value=content)
        sheet.cell(row=row[0].row, column=10, value=ret)

        # 保存修改后的文件
        wb.save('../result/testdtv6.xlsx')

def llmdata():
    # 打开xlsx文件
    wb = openpyxl.load_workbook('result/testdtv5.xlsx')
    sheet = wb.active

    # 假设数据从第 2 行开始
    rows = sheet.rows[153:]
    for row in tqdm(rows, desc="Processing", ncols=100): #
        if len(row) < 2:
            continue
        col1_value = row[1].value  # 获取第一列的值
        if col1_value =="原始问题":
            continue
        if col1_value == None:
            continue
        content = row[7].value
        # col2_value = row[2].value  # 获取第二列的值
        keywords = getKeywords(col1_value)
        tp = checkAB(col1_value)
        if "A" in tp:
            content = queryA(keywords)
        else:
            content = queryB(keywords)

        ret =""
        # try:
        #     ret = answerbyGraph(col1_value,content,[])
        # except Exception as e:
        #     print(e)
        #     print(len(content))
        
        # 在第三列写入 'Hello'
        sheet.cell(row=row[0].row, column=3, value=tp)
        sheet.cell(row=row[0].row, column=5, value=keywords)
        sheet.cell(row=row[0].row, column=6, value=content)
        sheet.cell(row=row[0].row, column=7, value=ret)

        # 保存修改后的文件
        wb.save('result/testdtv4.xlsx')


def refreshLashResult(lastxls,newxls):
    # 打开xlsx文件
    wb = openpyxl.load_workbook('result/' + lastxls)
    sheet = wb.active
    items = {}
    for row in sheet.rows: 
        if len(row) < 2:
            continue
        col1_value = row[1].value  # 获取第一列的值
        items[col1_value] = row
    
    wb2 = openpyxl.load_workbook('result/' + newxls)
    sheet2 = wb2.active

    for row in sheet2.rows:
        if len(row) < 2:
            continue
        col1_value = row[1].value  # 获取第一列的值
        if col1_value not in items:
            continue
        if col1_value =="原始问题":
            continue
        if col1_value == None:
            continue
        ret = items[col1_value][7].value
        if not ret:
            continue
        
        ans1 = items[col1_value][6].value
        ans2 = row[6].value
        
        if ret =="本次不测":
            sheet2.cell(row=row[0].row, column=8, value=ret)
        elif ans1 in ans2.replace("_x000D_",""):
            sheet2.cell(row=row[0].row, column=8, value=ret)
        else:
            sheet2.cell(row=row[0].row, column=8, value="待重新确认")
            sheet2.cell(row=row[0].row, column=9, value=ans1)


    # 保存修改后的文件
    wb2.save('result/' + newxls)




        
    

if __name__ == "__main__":
    ScanData()
    # llmdata()

    # refreshLashResult("testdtv4.xlsx","testdtv5.xlsx")
    pass