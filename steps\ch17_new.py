import pandas as pd
import requests
from tools import qwenMax


# 假设的接口 URL
first_api_url = 'http://192.168.219.27:8080/qy-epoint-web/rest/klknowledgerest/getNoKnowledgeMicrodomainList'
third_api_url = 'http://192.168.219.27:8080/qy-epoint-web/rest/klknowledgerest/addOrUpdateKonwledge'

# 第一步：请求接口获取 list 对象数据
def get_list_data():
    try:
        payload = {
            "privatekey":"1",
            "topN":"100",
            "domain_id":"008d3fef-da26-11ef-9151-08bfb8b9824c"
        }
        response = requests.post(first_api_url, json=payload)
        response.raise_for_status()  # 检查请求是否成功
        return response.json()
    except requests.RequestException as e:
        print(f"请求接口失败: {e}")
        return []

# 第二步：遍历 list 并处理数据
def process_list_data(data_list):
    for item in data_list:
        entity_type_id = item.get('entity_type_id')
        condition_name = item.get('condition_name')
        intent_name = item.get('intent_name')
        intent_id = item.get('intent_id')
        condition_id = item.get('condition_id')
        entity_type_name = item.get('entity_type_name')

        # 定义 queryTxt 字符串
        queryTxt = '我是一个*主体类型*，想要*意图*，但是*情形*，请问在这样的情况下，给我尽可能详细的指导信息，以及在深圳市如何申请办理的信息，以便我了解并应对可能的问题。'

        # 替换占位符
        if entity_type_name:
            queryTxt = queryTxt.replace('*主体类型*', entity_type_name)
        else:
            queryTxt = queryTxt.replace('我是一个*主体类型*，', '')
        if intent_name:
            queryTxt = queryTxt.replace('*意图*', intent_name)
        else:
            queryTxt = queryTxt.replace('想要*意图*，', '')
        if condition_name:
            queryTxt = queryTxt.replace('*情形*', condition_name)
        else:
            queryTxt = queryTxt.replace('但是*情形*，', '')

        messages = [
            {
                "role": "user",
                "content": queryTxt
            }
        ]

        try:
            # 获取响应数据
            result = qwenMax(messages)
            # 将接口返回的数据作为参数，再次调用接口
            json = {
                "privatekey": "1",
                "domain_id": "008d3fef-da26-11ef-9151-08bfb8b9824c",
                "entity_type_id": entity_type_id,
                "intent_id": intent_id,
                "condition_id": condition_id,
                "knowledge":
                    [
                        {
                            "knowledge": result,
                            "source": "",
                            "adjustarea": ""
                        }
                    ]
            }
            call_third_api(json)
        except requests.RequestException as e:
            print(f"请求发生错误: {e}")

# 第四步：调用第三个接口
def call_third_api(data):
    try:
        response = requests.post(third_api_url, json=data)
        response.raise_for_status()  # 检查请求是否成功
        print("第三个接口返回数据:", response.json())
    except requests.RequestException as e:
        print(f"调用第三个接口失败: {e}")

if __name__ == "__main__":
    list_data = get_list_data()
    list_data = list_data['micro_domain']
    if list_data:
        process_list_data(list_data)

