



###
POST http://192.168.186.65/inteligentsearch/rest/esinteligentsearch/getByVector
# POST http://192.168.207.190:8080/inteligentsearch/rest/esinteligentsearch/getByVector
Content-Type: application/json
Authorization: Basic admin admin


{
        "wd": "法人变更 合伙公司",
        "fields": "path",
        "re_fields": "path;content",
        "vecfield": "pathvec",
        "cnums": "nzyxzrgs27",
        "pn": 0,
        "rn": 10,
        "vecType": "1",
        "pluginName": "EmbeddingOperatorNew",
        "isSynonym": "1",
        "esdsid": 8,
        "accuracy": 15,
        "rescore": "{'inteligentsearchType':'equalratio','sim1':'pathorg'}",
        "opCondition": null,
        "opType": "0"
    }


    

###
POST http://192.168.186.65/inteligentsearch/rest/esinteligentsearch/getByVector
# POST http://192.168.207.190:8080/inteligentsearch/rest/esinteligentsearch/getByVector
Content-Type: application/json
Authorization: Basic admin admin


{
    "wd": "办理方式 注销登记 新设登记",
    "fields": "kw",
    "re_fields": "path;content",
    "vecfield": "contentvec",
    "cnums": "nzyxzrgs19",
    "pn": 0,
    "rn": 10,
    "vecType": "1",
    "pluginName": "EmbeddingOperatorNew",
    "isSynonym": "1",
    "esdsid": 8,
    "accuracy": 15,
    "rescore": "{'inteligentsearchType':'equalratio','sim1':'kworg'}",
    "opCondition": null,
    "opType": "0"
}


###
POST http://192.168.219.30:8080/epoint-blbb-web/rest/dynamicapi/callES
Content-Type: application/json
Authorization: Basic admin admin

{
    "question": "办理方式 注销登记 新设登记",
    "index_name": "nzyxzrgs19",
    "queryfield": "content",
    "vectorfield": "content",
    "selectfields": "path;content",
    "num": 10
}
