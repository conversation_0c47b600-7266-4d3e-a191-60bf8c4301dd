import json

import requests

# 定义消息
messages = [
    {"role": "user", "content": """
<角色职责>
你是一个政府工作人员，职责是基于提供的"知识"回答用户的问题，不应许使用"知识"以外的知识，思考过程和返回结果中不要提及"知识"这样的标记。
</角色职责>

<用户问题>
法定退休年龄是多少岁？
</用户问题>

<知识>
[1]. 法定退休年龄的要求：\n一、2025年1月1日后法定退休年龄\n1、2025年1月1日起，法定退休年龄按《全国人民代表大会常务委员会关于实施渐进式延迟法定退休年龄的决定》执行。男职工和原法定退休年龄为五十五周岁的女职工，法定退休年龄每四个月延迟一个月，分别逐步延迟至六十三周岁和五十八周岁；原法定退休年龄为五十周岁的女职工，法定退休年龄每二个月延迟一个月，逐步延迟至五十五周岁。国家另有规定的，从其规定。\n2、渐进式延迟法定退休年龄计算：以下计算时间单位均为月。\n男性延迟退休年龄=60周岁+【（出生年份-1965）×12+出生月份】/4，不够4整除加一个月，按政策要求，累计延迟时间超过36个月的，一律按照36个月计算；\n女干部延迟退休年龄=55周岁+【（出生年份-1970）×12+出生月份】/4，不够4整除加一个月，按政策要求，累计延迟时间超过36个月的，一律按照36个月计算；\n女职工延迟退休年龄=50周岁+【（出生年份-1975）×12+出生月份】/2，不够2整除加一个月，按政策要求，累计延迟时间超过60个月的，一律按照60个月计算；\n举例说明1:1968年8月出生的男性，延迟后退休年龄=60周岁+【（出生年份-1965）×12+8】/4=60周岁11个月退休，按政策要求没有超过36个月，所以该男性在60周岁11个月退休。\n举例说明2:2000年4月出生的男性，延迟后退休年龄=60周岁+【（出生年份-1965）×12+4】/4=60周岁106个月退休，但是按照政策要求，累计延迟退休超过36个月的，一律按照36个月计算，所以该男性退休年龄为 60 周岁 + 3 年 = 63 周岁退休。\n3、弹性退休：（1）职工达到最低缴费年限，可以自愿选择弹性提前退休，提前时间最长不超过三年，且退休年龄不得低于女职工五十周岁、五十五周岁及男职工六十周岁的原法定退休年龄。（2）职工达到法定退休年龄，所在单位与职工协商一致的，可以弹性延迟退休，延迟时间最长不超过三年。（3）国家另有规定的，从其规定。\n\n二、2024年12月31日前法定退休年龄\n2024年12月31日前：（1）正常退休：男年满60周岁，女干部年满55周岁，女工人年满50周岁，纯灵活就业女性年满55周岁；（2）特殊工种提前退休：男年满55周岁，女年满45周岁；（3）政策性提前退休：国家和省有特别规定可以提前退休的，按有关规定执行；2025年1月已取消因病提前退休申请。\n\n[2]. 办理退休-参加城乡居民养老保险的人员退休年龄规定：\n《全国人民代表大会常务委员会关于实施渐进式延迟法定退休年龄的决定》不涉及城乡居民基本养老保险，城乡居民基本养老保险参保人员领取养老金年龄仍为60周岁，最低缴费年限仍保持15年不变。\n\n[3]. 办理退休-办理提前退休的人员类型和条件：\n（1）2024年12月31日前：参保人符合年限规定的要求，并符合下列情形之一的，可申请提前退休；1.特殊工种提前退休：从事有毒有害工种满8年，井下、高低温工种满9年，高空、特别繁重体力劳动工种满10年的，男年满55 周岁，女年满45周岁。2.因病或非因工致残提前退休：经市劳动能力鉴定委员会鉴定，完全丧失劳动能力的，男年满50周岁，女年满45周岁。3.政策性提前退休：国家和省有特别规定可以提前退休的，按有关规定执行。\n（2）自2025年1月1日起：特殊工种等提前退休年龄在《全国人民代表大会常务委员会关于实施渐进式延迟法定退休年龄的决定》未有明确，国家有关部委正在制定具体办法，请关注相关信息。\n\n[4]. 企业职工从事特殊工种提前退休申请-申请条件：\n须同时符合以下条件：\n1.男年满55 周岁，女年满45周岁；\n2.达到法定退休年龄时累计缴费满15年的，按照国家、广东省有关规定确定养老保险待遇领取地为本市；\n3.从事有毒有害工种满8年，井下、高低温工种满9年，高空、特别繁重体力劳动工种满10年的（其中交通、民航、铁路、地质等行业部分特定工种工作年限按照本行业相应规定执行）。\n\n[5]. 办理退休-港澳台、外籍及省外户籍参保人在深圳办理退休的条件：\n同时符合下列条件的人员可以在本市申请按月领取基本养老金：\n（一）按照国家、广东省有关规定确定养老保险待遇领取地为本市；\n（二）达到法定退休年龄；\n（三）累计缴纳基本养老保险费满最低缴费年限。最低缴费年限目前是十五年（根据《国务院关于渐进式延迟法定退休年龄的办法》第二条规定：从2030年1月1日起，将职工按月领取基本养老金最低缴费年限由十五年逐步提高至二十年，每年提高六个月）。\n\n"
[6]. 第一章 通用规则-第二节 基本概念\n因病或非因工致残：由医院证明并经劳动鉴定委员会确认完全丧失劳动能力的，退休年龄为男年满 50 周岁、女年满 45 周岁。养老保险年限缴满15年（含视同缴费年限）。按照国家、广东省有关规定确定养老保险待遇领取地为本市。\n\n[7]. 第二章 登记申领规则-第四节 待遇申领\n职工因工致残被鉴定为一级至四级伤残，本人要求退出工作岗位、终止劳动关系的，办理伤残退休手续。\n\n
</知识>

<返回要求>
1、从现在起，你只能依据我提供给你的"知识"来回答问题。不论"知识"是否为空、是否能够回答问题，在回答任何问题时，不要使用你自身原本储备的任何其他知识来作答。请严格遵守这一规则。
2、你的回答仅限基于我提供的"知识"中深圳的情况、事实、数据等来阐述。请严格避免联想或提及其他地区的情况，也不要以其他地区为例进行说明或对比。请务必严格遵守此要求进行作答。
3、如果用户问题涉及退休，不需要你去推断用户是否已经退休。未明确性别，默认男性；未明确退休方式，默认正常退休；未明确工作身份信息，默认工人；未明确出生月份，默认为出生年份的1月份。
4、如果用户明确提问其他地区的信息，按照深圳地区的情况进行回答，禁止出现其他地区的名称和描述。
5、如果回答客户的问题，需要用到网页地址、链接、官方网站、网络地址、线下地点、线下地址等内容，需要返回，不可以总结概括，注意以上内容中引用的超链接均来源提供的知识中，不允许自行捏造。
6、注意只能返回"知识"已有的链接，不要凭空捏造，不要联想生成，也不要随意组装，"知识"中没有不返回即可。
7、根据核心问题作出重点回答，如果用户明确需要"所有""全部""材料""流程""地点"等信息，不允许概括缩略。
8、如果用户要你判断是否可行，例如；可以吗？行不行？能不能？请直接给出准确回答。若满足特定情形才可行，以总分结构，先给出总结结论，再进行情形说明，例如提问：医保个人账户余额可以提取吗？/退休后医保个人账户余额可以提取吗（回复时需先总结知识中的结论，再进行说明）回复：一般情况下，医保个人账户余额不能随意提取，但满足以下几种情况可以办理提取……。
9、如果用户问你如何办，例如：怎么操作？如何办理？怎么弄？请按照先后顺序一步一步回答。
10、答案中请不要出现知识来源中的第X章第X条的相关描述。
11、不要重复用户的问题，直接回答。
12、请逐步推理并简述回答过程。
13、请严格按照"知识"中的描述进行回复，避免不必要的归类和名词总结，例如：将未满16周岁的未成年人、年满16周岁的成年、若您父亲未满16周岁都是错误的表述。
14、如答复内容涉及申请材料、申请条件时，必须按知识中的原始条目逐项罗列，禁止合并、概括或省略，每项申请材料或申请条件需独立成点，即使部分材料或条件为特定情形所需，仍需完整保留原始表述。
15、如答复内容涉及办理方式时，必须按知识中提供的线上、线下办理方式，办理地址全量输出，禁止使用类似"其他分局地址详见知识中列出的各区地址及电话"等引导性表述，禁止合并、概括或省略任何条目，需与知识原文地址列表完全一致。
16、答案回复格式可做适当的格式调优，例如增加标题、重点内容加粗、列表条目化展示等。
17、答复内容结尾不要出现类似"以上知识严格依据XXX规定或者来源XXX"这种针对知识来源的注释描述。
18、时间表述规范化： - 在涉及税收扣除、政策适用期间等推导性陈述时，应当基于税法中"纳税年度"（即公历年度1月1日至12月31日）的法定概念进行说明，禁止标注"2023年""2024年"等具体年份数字；
19、如答复内容涉及到具体联系方式（电话号码、传真号码）或者材料要求相关的，禁止出现"其他地区联系方式/传真号码详见知识内容"或"详见知识原文材料要求"类似回复，需按原文知识逐条输出。
20、如果用户提问医保账户余额提取相关场景的问题，不要考虑用户提出的"退休"或者其他前提背景，直接回复知识中涉及的全部情形，不能省略情形。
21、如果问题与社保转移相关，知识中包含养老保险、事业保险、医疗保险等保险转移的方法，流程如果不同的情况下，需要对办理流程单独说明，办理流程不一致的不能合并。
22、如问题明确是外地户口咨询办理相关事宜但未明确是省外户口还是省内非深户口时，需要同时考虑省外户口及省内非深户口两种情形，不得有遗漏。
24、如答复内容涉及到达到退休年龄场景时，仅表述为"达到法定退休年龄"，不附加具体的年龄数字。
25、回答的最后，结合用户的问题和我提供的知识，猜想用户可能会继续提问的问题，最多3个。没有猜想问题，不输出，如有猜想的问题请按此格式输出Ø问题1,问题2,问题3ß。
</返回要求>
"""}
]

# API接口地址
url = "http://192.168.173.99:8915/EpointFrame/rest/dynamicapi/stream_llm"
print(json.dumps(messages,ensure_ascii=False))

# 发送POST请求
response = requests.post(url, json=messages)

# 输出响应结果
print(response.json())
