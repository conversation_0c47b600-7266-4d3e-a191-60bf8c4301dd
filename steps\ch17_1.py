import json

import pandas as pd
import requests

from tools import get_prompt16, qwenMax,deepseek7b,qwen7b, qwenMax2
# 将用户的问题，拆分为寒喧、情绪、业务问题
# TODO 调一调提示词

# question = "内资企业怎么注销"
# question = "你好"
# question = "你们办理效率太低了"
# question = "你们办理效率太低了，内资企业怎么注销"
# 读取 Excel 文件
file_path = r'C:\Users\<USER>\Downloads\0218.xlsx'  # 替换为你的 Excel 文件路径
df = pd.read_excel(file_path, engine='openpyxl',keep_default_na=False)

# 循环遍历 DataFrame 的每一行
for index, row in df.iterrows():
    question = row['原始问题']
    if question != "":
        messages = [
            {
                "role": "user",
                "content": get_prompt16("ch21.txt", question, "", "")
            }
        ]
        response = qwenMax2(messages)

        result = json.loads(response)

        try:
            # 获取响应数据
            df.at[index, '主体'] = result['签证类型']
            df.at[index, '意图'] = result['意图']
            df.at[index, '情形'] = result['情形']
            df.to_excel(file_path, index=False)
            print(index)
        except requests.RequestException as e:
            print(f"请求发生错误: {e}，行索引: {index}")





# 总控模块，在拆出业务问题时就拆出多个问题
