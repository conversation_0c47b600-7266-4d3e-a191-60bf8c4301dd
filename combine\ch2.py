# 版本备份，简单steps流程串联起来
#判断情绪，咨询、办理，如果咨询，调用图谱查询。

# 测试数据：
# 我觉得政府办事效率很高，但有的事项最好能再快一些，我帮朋友咨询一下办理律师注销后重新执业需要多长审批时间？
# 您好！我是广西户籍，法学本科生，2020年通过法考，2022年取得中国律师执业证，2024年12月注销了律师证，现在公司做法务。我的配偶是香港永久居民，我现在拟通过结婚方式赴港定居取得香港身份。请问，如果我取得了香港身份证，我还可以在深圳申请恢复律师执业吗？
# 我今年40岁，是壮族，2016年自己考过了国家司法考试，不是法院的退休人员。 请问申请首次注册需要提交什么材料？
# 第一个问题是我来自广西，想咨询一下办理律师执业变更需要满足什么条件？还有一个问题是我表弟是香港公民，他如果要来深圳进行律师首次执业注册需要提交什么材料？
# 本人户籍所在地是贵州，本人于2022年4月在贵州省通过参加法考获得法律职业资格证书C证，现本人想在深圳市注册的律师事务所申请实习律师，请问获得法律职业资格证书C证能否在深圳市的律师事务所申请实习律师。我想确认一下非深圳户籍人员持有法律职业资格证书C证能否在深圳市的律师事务所申请实习律师？另外，能否告诉我申请首次注册需要提交什么材料？


import requests
import json
from dashscope import Generation
import random
import os
import re
import sys

root_path = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
sys.path.append(root_path)
from steps.tools import  get_prompt, qwenMax


def emocheck(query,chat_history):
    messages = [{
        "role": "user",
        "content": get_prompt("emocheck_template.txt",query,[])
    }]
    response = qwenMax(messages)
    if response != "" and response[0] =='2':
        return 2,""
    elif response != "" and response[0] =='3':
        return 3,""
    elif response != "" and response[0] =='0':
        return 0,response[2:]
    elif response != "" and response[0] =='1':
        return 1,response[2:]
    else:
        return "", response

def getKeywords(query,chat_history):
    for i in range(5):
        try:
            messages = [{
                "role": "user",
                "content": get_prompt("getkeywords_template.txt",query,[])
            }]
            response = qwenMax(messages)
            json_data = json.loads(response)
            return json_data["关键词"]
        except:
            continue
    return response

def queryGraph(keywords):
    url = "http://192.168.186.65:5000/querygraph"

    payload = "{\"keywords\":\"" + keywords +"\",\"graphurl\":\"http://172.29.255.220:34008/relation_frzssk/gremlin\",\"index_name\":\"frzssk\"}"
    headers = {
        'user-agent': "vscode-restclient",
        'content-type': "application/json"
        }

    response = requests.request("POST", url, data=payload, headers=headers)

    print(response.text)
    return response.json()["finalResult"]

def answerbyGraph(query,graphcontent,chat_history):
    prompt = get_prompt("answer_template.txt",query,[])
    prompt = prompt.replace("{graphcontent}", graphcontent)
    
    messages = [{
        "role": "user",
        "content": prompt
    }]
    response = qwenMax(messages)
    return response


while True:
    query = input("请输入: ")#.split()
    # flag = int(flag)
    chat_history =[]
    g_assistant_response = None
    status = None
    status_table = []
    
    print("等待模型的回答ing...")
    emotp, response = emocheck(query,chat_history)
    if emotp == 0 or emotp == 1:
        print(f"模型的回答：{response}")
    
    print("机器人判定",emotp)
    if emotp == 1 or emotp == 2: #咨询或 情绪
        lst = getKeywords(query,chat_history)
        txt = queryGraph(lst)
        ret = answerbyGraph(query,txt,chat_history)
        print(f"模型的回答：{ret}")
        continue
    
    if emotp == 3: #办理类
        lst = getKeywords(query,chat_history)
        # txt = queryGraph(lst)
        ret = answerbyGraph(query,'',chat_history)
        print(f"模型的回答：{ret}")
        continue
        
        
    print(f"模型的回答：{response}")
    




            