import json
from collections import defaultdict
from typing import List, Dict, Any
import requests

AUTH_TOKEN = "2ef6c6e1accda0858af025c724b6a007-zwbg-soa"

COOKIES = "_font_size_ratio_=1.0; epoint_local=zh_CN; access_token_expiresin=1800; _idea_skin_=cyanine; _theme_=idea; sid=55b4a46fb13be1b0bdbfb680ae94fcc4-zwbg-soa; EPTOKEN=1567A8F27C89B2893D7607CCDFFF5E4607BA4D5355F4C119C33C3C2B6B7FB76D; access_token=55b4a46fb13be1b0bdbfb680ae94fcc4-zwbg-soa; refresh_token=7f940d90e261c67f91feac77e0f559d4-zwbg-soa"


def call_es_api(data: Dict[str, Any]) -> Dict[str, Any]:
    url = "http://192.168.173.99:8915/EpointFrame/rest/dynamicapi/calles_table"
    headers = {
        "Content-Type": "application/json",
        "Cookie": COOKIES
    }
    response = requests.post(url, data=json.dumps(data), headers=headers)
    return response.json()


def queryRecord(
        question: str,
        fields: str,
        num: int,
        index_name: str,
        isfulltext: bool = True,
        vector: str = ""
) -> List[Dict[str, Any]]:
    record = {
        "question": question,
        "index_name": index_name,
        "fields": fields,
        "selectfields": "tablename;tablenamedes",
        "orgfield": "",
        "num": num,
        "isfulltext": isfulltext
    }
    if not isfulltext and vector:
        record["vectorfield"] = vector
    response = call_es_api(record)
    records = response.get("content", {}).get("result", {}).get("records", [])
    tablename_dict = {}
    for item in records:
        tablename = item.get("tablename", "")
        tablenamedes = item.get("tablenamedes", "")
        if tablename and tablename not in tablename_dict:
            tablename_dict[tablename] = tablenamedes
    result_list = [{"tablename": k, "tablenamedes": v} for k, v in tablename_dict.items()]
    return result_list


def api(params: Dict[str, Any], header: Dict[str, Any]) -> Dict[str, Any]:
    question = params.get("question")
    top = int(params.get("top", 0) or 0)
    num = 20
    ret1 = queryRecord(question, "tablenamedes", 1, "chatbitable0522002",False, "tablenamedes")
    # ret2 = queryRecord(question, "fieldnamedes", 1, "chatbitable0522002",False, "fieldnamedes")
    ret3 = queryRecord(question, "content", 1, "chatbidata0522002",True, "")

    seen_tablenames = set()
    merged_results = []
    for item in ret1+ret3:
        tablename = item.get("tablename", "")
        if tablename and tablename not in seen_tablenames:
            seen_tablenames.add(tablename)
            merged_results.append(item)

    # reranker_url = "http://192.168.173.99:5008/reranker/predict"
    # reranker_headers = {"Content-Type": "application/json"}
    # text_list = [item['tablenamedes'] for item in merged_results]
    # record2 = {
    #     "text": question,
    #     "compare_list": text_list
    # }
    # try:
    #     reranker_resp = requests.post(reranker_url, data=json.dumps(record2), headers=reranker_headers)
    #     reranker_results = reranker_resp.json()
    #     results_reranker = reranker_results.get("result", [])
    # except Exception as e:
    #     print(f"重排接口调用失败: {e}")
    #     results_reranker = []
    #
    # final_results = []
    # for reReranker in results_reranker:
    #     content, score = reReranker
    #     if content in text_list:
    #         index = text_list.index(content)
    #         item = merged_results[index]
    #         final_results.append(item)
    final_results = merged_results
    if final_results:
        return {"result": final_results[:top]}
    else:
        return {"result": []}

from flask import Flask, request, jsonify

app = Flask(__name__)

@app.route('/api/tablenamesearch', methods=['POST'])
def tablenamesearch_rest():
    try:
        params = request.get_json(force=True)
        header = dict(request.headers)
        result = api(params, header)
        return jsonify(result)
    except Exception as e:
        return jsonify({"error": str(e)}), 500

if __name__ == "__main__":
    # print("开始")
    # params = {
    #     "question": "普瑞光电（厦门）股份有限公司SAP系统采购项目处于什么环节？",
    #     "top": 1
    # }
    # header = {
    #     "Content-Type": "application/json"
    # }
    # print(api(params, header))
    app.run(host="127.0.0.1", port=5000, debug=False)
