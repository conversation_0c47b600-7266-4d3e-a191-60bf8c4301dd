#jzm demo 思路

#测试输入：
#我想办理失业金领取事项 0
#你好 0



import requests
import json
from dashscope import Generation
import random
import os
import re
import sys
import os

# 获取根目录路径
root_path = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
sys.path.append(root_path)

from steps.tools import  qwenMax


# 全局状态
status_to_question = None
question_list = None
status_table = None
gold_answer = None
table_prompt_list = None


def get_prompt(path,query,history):
    prompt = "".join( open("prompts_jzm/" + path, "r", encoding = "utf-8").readlines() )
    prompt = prompt.replace("{query}", query)
    prompt = prompt.replace("{sentence}", query)
    prompt = prompt.replace("{chat_history}", json.dumps(history, ensure_ascii=False))
    
    # 构建 JSON 文件路径
    base_name = path.replace("_template.txt","")
    json_file = os.path.join("template", base_name + ".json")
    if os.path.exists(json_file):
        with open(json_file, "r", encoding="utf-8") as f:
            json_data = json.load(f)  # 读取 JSON 内容
            json_str = json.dumps(json_data, ensure_ascii=False)  # 转为字符串
            lstname = "{" +base_name + "_list}"
            prompt = prompt.replace(lstname, json_str)  # 替换 {{json名}}
            
    return prompt

def recognition_status(query):     
    global status_to_question,question_list,status_table,gold_answer,table_prompt_list
    messages = [{
        "role": "user",
        "content": get_prompt("recognition_status_template.txt",query,[])
    }]
    response = qwenMax(messages)
    import re
    pattern = r"【(.*?)】"
    matches = re.findall(pattern, response)
    if len(matches) == 0:
        response = "None"
    else:
        response = matches[-1]
    if response != "None":
        if status_to_question is None:
            with open("template/condition_question_list.json", "r", encoding="utf-8") as f:
                status_to_question = json.load(f)
        if response in status_to_question:
            # import pdb; pdb.set_trace()
            question_list = status_to_question[response]["question_list"]
            status_table = status_to_question[response]['status_table']
            gold_answer = status_to_question[response]['gold_answer']
            table_prompt_list = status_to_question[response]["table_prompt_list"]
    return response
        
def classification(query):   
    messages = [{
        "role": "user",
        "content": get_prompt("query_classification_template.txt",query,[])
    }]
    question_class = qwenMax(messages)
    if "不需要借助" in question_class:
        question_class = "不需要借助"
    elif "需要借助" in question_class:
        question_class = "需要借助"
    else:
        question_class = "不需要借助"
    is_rag = question_class == "需要借助"
    return question_class, is_rag

def recognition_intention(query):
    messages = [{
        "role": "user",
        "content": get_prompt("intention_template.txt",query,[])
    }]
    response = qwenMax(messages)

    pattern = r"\[.*?\]"
    matches = re.findall(pattern, response)
    response = eval(response)
    if len(matches) == 0:
        response =  '["None"]'
    else:
        response = matches[-1]
        
    if isinstance(response, str):
        try:
            response = eval(response)
        except:
            response = ["None"]
    elif isinstance( response, list ):
        response = response
    return response

def reconstruct_history( history):
    result = []
    for i in history:
        result.append( (i["role"], i["context"]) )
    return result

def req( query, url, knowledge_base_name = "", history = []):
    history = reconstruct_history(history)
    payload = json.dumps({
                    "query": query,
                    "knowledge_base_name": knowledge_base_name,
                    "history": history,
                    "score_threshold": 0.,
                    "topk": 5,
                    "stream": False,
                    "return_docs": True,
                    "multi_query": False
                    })

    headers = {
            'Content-Type': 'application/json'
            }

    response = requests.request("POST", url, headers=headers, data=payload) 

    result = []

    for chunk in response.iter_lines(decode_unicode=True):
        if chunk:
            if chunk.startswith("data: "):
                data = json.loads(chunk[6:].strip())
            elif chunk.startswith(":"):
                continue
            else:
                data = json.loads(chunk)
            result.append( data )

    res_dict = result[-1]

    return {
        "id": id,
        "question": query,
        "answer": res_dict['result'].strip(),
        "docs": res_dict["docs"]
    }

def auto_fill_table(query,history,table):
    
    response_list = []

    for prompt in table_prompt_list:
        prompt = prompt.replace("{query}", query)
        prompt = prompt.replace("{chat_history}", json.dumps(history, ensure_ascii=False))
            
        messages = [{
            "role": "user",
            "content": prompt
        }]
        response = qwenMax(messages)
        response_list.append(response)
        
    # 修改表格中的值
    for idx, key in enumerate( list(table.keys()) ):
        table[key] = response_list[idx]
   
intention_mapping_to_knowledge_base_name = {
    "失业": "shiye"
}
 
def rag(query,history): 
    print("模型思考过程...")

    # 根据query和history来确定 status     
    status = recognition_status(query)
    print(f"判断当前用户办理事项状态: {status}")

    if status and status in ["失业金领取事项"]:
        print( f"填表..." )
        print(f"填表之前的表格信息：{status_table}...")
        auto_fill_table(query,history,status_table)
        print(f"填表之后的表格信息：{status_table}...")
        
    # 需要一个接口来接受业务系统的问题和表格
    # 对于业务系统来的问题，我们需要判断用户的输入是否符合该问题，如果符合就填表，如果不符合就走fast qa和正常rag
    # 对于非业务系统来的问题，我们走fast qa和正常rag
    # 
    # 与事项不相关，但是又沾点边的问题

    # 我想办理失业金领取事项
    print("进行fast qa匹配...")

    result = None# self.fast_qa.match(query)

    if not result:
        print("fast qa没有匹配到...")
    else:
        print("fast qa成功匹配到问题！！！ ")
        return result, status
    # 判断问题需不需要rag
    question_class, is_rag = classification(query)

    print(f"是否需要借助外部知识库：{question_class}")
    print( f"是否需要使用rag: {is_rag}" )
    if is_rag:
        query_intention = recognition_intention( query )
        print( f"查询到的外部知识库为: {query_intention}" )
        # 需要知识库的介入，但是又没有识别出意图来，我们需要重写这个query，根据上下文
        if query_intention == ["None"]:
            print(f"需要借助外部知识库，但是查询到的外部知识库为None（即没有查询到外部知识库），需要进行问题重写...")
            old_query = query
            rewrited_query = rewrite_query_by_history(query, show_conversation())

            if rewrited_query:
                query = rewrited_query

            query_intention = recognition_intention( query )
            print(f"重写之前的query: {old_query}")
            print(f"重写之后的query: {rewrited_query}")
            print( f"确定重写之后的query所需要的知识库: {query_intention}" )
    
    if is_rag and "None" not in query_intention:
        chat_url = "http://192.168.186.11:7861" + "/chat/knowledge_base_chat"

        # 根据意图获得相应的知识库(首先将范围局限在单意图)
        knowledge_base_name = intention_mapping_to_knowledge_base_name[ query_intention[0] ]
        print( f"在知识库：【{knowledge_base_name}】进行rag查询，并且回答问题..." )
        result = req(query = query, url = chat_url, knowledge_base_name = knowledge_base_name, history = chat_history)
    else:
        print( f"借助模型本身知识回答问题..." )
        chat_url = "http://192.168.186.11:7861" + "/chat/ordinary_chat"
        result = req(query = query, url = chat_url, history = chat_history)
    
    return result["answer"], status

def show_conversation(chat_history):
    conversation = ""
    for chat in chat_history:
        conversation += f"【{chat['role']}】: {chat['context']}"
        conversation += "\n"

    with open( "conversation_history.json", "w", encoding="utf-8" ) as f:
        for i in chat_history:
            f.write( json.dumps(i, ensure_ascii=False, indent = 4) )
            f.write( "\n" )

    return conversation

while True:
    query, flag = input("请输入: ").split()
    flag = int(flag)
    chat_history =[]
    g_assistant_response = None
    status = None
    status_table = []
    
    if flag == 0:
        print("等待模型的回答ing...")
        response, status = rag(query,chat_history)
        g_assistant_response = response
        g_status = status
        
        # 将这一条历史信息加入历史对话中
        chat_history.append( { "role": "user", "context": query } )
        chat_history.append( { "role": "assistant", "context": response } )
        print(f"当前表格状态：{status_table}...")
        print(f"展示历史对话信息：\n{show_conversation(chat_history)}")
        
        # 进行重置
        # 模型的响应和问题
        g_assistant_response = None
        assistant_query = None

        # 用户的响应和问题
        user_response = None
        user_query = None
        
        
        # 返回给业务的东西
        print(f"返回给业务的东西：事项状态：{status}, 状态表：{status_table}...")

        # 返回当前status和表格给业务组
        #  self.status, self.status_table, response



    
            