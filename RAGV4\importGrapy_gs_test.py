# -*- coding: utf-8 -*-
"""
@Des     : 检索图谱中的数据，插入到库， 作为A类问题
检索word中的数据，拆分，插入库库，作为B类问题
"""
import datetime
import re
from uuid import uuid4
import json
import sys
import os
import pandas as pd
import requests
from requests.models import HTTPBasicAuth
from steps.tools import qwenMax

def createIndex(index_name,issz):
    if issz=="1":
        esdsid=4
        path = "D://graph//schema.json"
        inteligentsearch_url = "http://10.132.36.149/epoint-blbb-web/rest"
    else:
        esdsid = 2
        parent_dir = os.path.dirname(os.getcwd())
        path = os.path.join(parent_dir, 'RAGV2', 'schema_test.json')
        # inteligentsearch_url = "http://192.168.186.65/inteligentsearch/rest"
        inteligentsearch_url = "https://192.168.204.126/epoint-blbb-web-huadu/rest"

    init_data = json.load(open(path, encoding="utf-8"))
    init_data["esdsid"] = esdsid
    init_data["category"]["categoryName"] = index_name
    init_data["category"]["categoryDescribe"] = index_name
    init_data["category"]["categoryNum"] = index_name

    init_data['category'] = json.dumps(init_data['category'])
    build_url = inteligentsearch_url + "/esinteligentsearchmanageinit/addCategory"
    headers = {
        'Content-Type': 'application/json',
        "Authorization": "Bearer e5234ffec5ea7e0228124797f6f9cfbe-zwbg-soa",
        "User-Agent": "python"
    }

    response = requests.post(url=build_url, json=init_data, headers=headers,verify=False)
    print(response.text)


def deleteIndex(index_name,issz):
    if issz=="1":
        esdsid=4
        inteligentsearch_url = "http://10.132.36.149/epoint-blbb-web/rest"
    else:
        esdsid = 2
        # inteligentsearch_url = "http://192.168.186.65/inteligentsearch/rest"
        inteligentsearch_url = "https://192.168.204.126/epoint-blbb-web-huadu/rest"
    init_data = {}
    init_data["categorynum"] = index_name
    init_data["esdsid"] = esdsid
    build_url = inteligentsearch_url + "/esinteligentsearchmanageinit/deleteCategoryByNum"
    headers = {
        'Content-Type': 'application/json',
        "Authorization": "Bearer " + "token",
        "User-Agent": "python"
    }

    response = requests.post(url=build_url, json=init_data, headers=headers,verify=False)
    print(response.text)


def embding(q_list,issz):
    if issz=="1":
        embed_url = "http://10.132.36.149/get_embedding/embed"
    else:
        embed_url = "http://192.168.186.18:9081/linezhengshi_embedding/embed"
    data_embed = {"text": q_list}
    headers = {
        'Content-Type': 'application/json',
        "User-Agent": "python",
        "Authorization": "Bearer 4b1f2daed98265cc43aa7ebccfa89f0e",
    }
    response = requests.post(url=embed_url, data=json.dumps(data_embed), headers=headers)
    results = response.text
    return json.loads(results)["result"]


def insertData(ptype, index_name, purpose, subkey, content, source, taskname, situationorg, domainname,issz,order,kw2):
    password="admin"
    if issz=="1":
        es_url = "http://10.132.36.149"
        password="wdsaf137yu"
    elif issz=="2":
        es_url = "http://192.168.38.136:8080"
    else:
        es_url = "https://192.168.186.63:9200"
    id = str(uuid4())

    time_now = datetime.datetime.now().strftime('%Y-%m-%d')
    situation = ""

    data = {
        "kw2_keyword": kw2,
        "kworg_keyword": subkey,
        "kw_text": subkey,
        "kwvec_vector": embding([subkey],issz)[0],

        "content_text": content,
        "contentvec_vector": embding([content],issz)[0],

        "ptypeorg_keyword": ptype,
        "ptype_text": ptype,
        "ptypevec_vector": embding([ptype],issz)[0],

        "purposeorg_keyword": purpose,
        "purpose_text": purpose,
        "purposevec_vector": embding([purpose],issz)[0],

        "situation_text": situation,
        "situationorg_keyword": situationorg,
        "situationevec_vector": embding([situation],issz)[0],

        "source_text": source,
        "taskname_text": taskname,
        "domainname_keyword": domainname,
        "order_keyword": order,
        "id_keyword": id, "infodate_date": time_now, "syscategory_keyword": index_name
    }
    data = json.dumps(data)

    headers = {'Content-Type': 'application/json'}
    insert_url = es_url + "/" + index_name + "/_doc" + "/"
    response = requests.put(insert_url + id, headers=headers, auth=HTTPBasicAuth("admin", password),
                            verify=False, data=data.encode('utf-8'))
    try:
        re = json.loads(response.text)
        if "error" in re:
            error = re["error"]["type"]
            print(re)
            print(f"[*] insert应用中，导入数据失败，请检查传参，错误类型{error}")
        else:
            # pass
            result = re["result"]
            print(f"[*] 导入数据成功，主键：{id}，状态：{result}")
    except Exception as e:
        re = e
        print(f"[*] insert应用中， 导入数据出错，请检查es地址是否访问通，错误类型{e}，es接口返回{response.text}")


def parseRuleExcel(filename,issz):
    if issz=="1":
        file_path = "D://graph//"+filename
    else:
        parent_dir = os.path.dirname(os.getcwd())
        file_path = os.path.join(parent_dir, 'data', filename)

    result = []
    df = pd.read_excel(str(file_path), keep_default_na=False)
    chapter = ""
    section = ""

    for index, row in df.iterrows():

        subjecttype = ""
        intent = ""
        source = ""
        order=""

        if str(row["章"]) != '':
            chapter = str(row["章"]) + "、" + str(row["章名称"])
            section = ""
        if str(row["节"]) != '':
            section = str(row["节"]) + "、" + str(row["节名称"])

        content = "第" + str(row["条"]) + "条，" + str(row["内容"])
        if str(row["法律法规来源"]) != '':
            source = str(row["法律法规来源"]) + "!!@@##" + str(row["法律法规明细"])

        if str(row["适用主体类型"]) != '':
            subjecttype = str(row["适用主体类型"])

        if "适用意图" in row:
            intent = str(row["适用意图"])

        if "排序号" in row:
            order = row["排序号"]
        else:
            order = ""

        result.append({
            "path": f"{chapter}-{section}",
            "content": content,
            "source": source,
            "subjecttype": subjecttype,
            "intent": intent,
            "order":order
        })
    return result


def parseGraphexcel(filename,issz):
    #excel直接转
    if issz=="1":
        file_path = "D://graph//"+filename
    elif issz=="2":
        file_path = "D://graph//"+filename
    else:
        parent_dir = os.path.dirname(os.getcwd())
        file_path = os.path.join(parent_dir, 'data', filename)

    all_sheets = pd.read_excel(file_path, sheet_name=None, keep_default_na=False)

    # 遍历所有工作表
    datalist = []
    for sheet_name, df in all_sheets.items():
        path = ["", "", ""]
        desc = ["", "", ""]
        for index, row in df.iterrows():
            obj = {}
            if str(row["一级节点"]) != '':
                path[0] = str(row["一级节点"])
                path[1] = ""
                path[2] = ""

                desc[0] = ""
                desc[1] = ""
                desc[2] = ""

            if str(row["二级节点"]) != '':
                path[1] = str(row["二级节点"])
                path[2] = ""

                desc[1] = ""
                desc[2] = ""

            if str(row["三级节点"]) != '':
                path[2] = str(row["三级节点"])
                desc[2] = ""

            if str(row["一级节点知识"]) != '':
                desc[0] = str(row["一级节点知识"])
                desc[1] = ""
                desc[2] = ""

            if str(row["二级节点知识"]) != '':
                desc[1] = str(row["二级节点知识"])
                desc[2] = ""

            if str(row["三级节点知识"]) != '':
                desc[2] = str(row["三级节点知识"])

            obj["path"] = str(sheet_name) + "-" + "-".join(filter(None, path))
            obj["desc"] = "\n".join(filter(None, desc))
            obj["subjecttype"] = row["适用主体类型"]

            if "适用意图" in row:
                obj["intent"] = row["适用意图"]
            else:
                obj["intent"] = ""

            if "排序号" in row:
                obj["order"] = row["排序号"]
            else:
                obj["order"] = ""

            datalist.append(obj)
    return datalist


def insertZntp(domainname, filename, index_name,issz,df):
    #插入指南图谱
    questionlist = parseGraphexcel(filename,issz)
    i = 1
    for object in questionlist:
        try:
            path = str(object["path"])
            desc = str(object["desc"])
            subjecttype = str(object["subjecttype"])
            intent = str(object["intent"])
            order= str(object["order"])

            sbitem = path.split("-")
            if "通用知识" in sbitem[0]:
                taskname = ""
            else:
                taskname = sbitem[2]
            subkey = path.replace(sbitem[0] + "-" + sbitem[1] + "-", "")
            #根据subkey提取关键词
            subkey2 = path.replace(sbitem[0] + "-", "")
            kw2=process_excel(subkey2,i,df)

            ptypelist = []
            intentlist = []
            if "全部" in subjecttype:
                ptypelist.append("all")
            elif "," in subjecttype:
                ptypelist = subjecttype.split(",")
            else:
                ptypelist.append(subjecttype)

            if intent=="":
                if domainname=="市场主体登记":
                    intentlist.append(sbitem[1])
            else:
                if "全部" in intent:
                    intentlist.append("all")
                elif "," in intent:
                    intentlist = intent.split(",")
                else:
                    intentlist.append(intent)

            # 数据导入
            for item in ptypelist:
                if len(intentlist) > 0:
                    for value in intentlist:
                        insertData(item, index_name, value, subkey, desc, "", taskname, "", domainname,issz,order,kw2)
                else:
                    insertData(item, index_name, "", subkey, desc, "", taskname, "", domainname, issz,order,kw2)

            print("总数：" + str(len(questionlist)) + "   当前：" + str(i))
            i = i + 1
        except Exception as e:
            print(e.args)
            # print(path)


def insertRule(domainname, filename, index_name,issz):
    lst = parseRuleExcel(filename,issz)
    for object in lst:
        path = object["path"]
        desc = object["content"]
        source = object["source"]
        subjecttype = object["subjecttype"]
        intent = str(object["intent"])
        order = str(object["order"])
        ptypelist = []
        intentlist = []
        if "全部" in subjecttype:
            ptypelist.append("all")
        elif "," in subjecttype:
            ptypelist = subjecttype.split(",")
        else:
            ptypelist.append(subjecttype)

        if intent!="":
            if "全部" in intent:
                intentlist.append("all")
            elif "," in intent:
                intentlist = intent.split(",")
            else:
                intentlist.append(intent)

        # 数据导入
        for item in ptypelist:
            if len(intentlist)>0:
                for value in intentlist:
                    insertData(item, index_name, value, path, desc, source, "", "", domainname,issz,order,"")
            else:
                insertData(item, index_name, "", path, desc, source, "", "", domainname,issz,order,"")

def process_excel(path,i,df):
    messages = [
        {"role": "system",
         "content": '1、提取问题中的关键词  2、无需分析过程，直接返回结果 3、返回json格式：{关键词:关键词1,关键词2,关键词3}'},
        {"role": "user", "content": path}
    ]
    result = qwenMax(messages)
    try:
        json_str = json.loads(result)
        # 写入到excel
        df.at[i, '路径'] = path
        df.at[i, '关键词'] = json_str['关键词']
        return json_str['关键词']
    except json.JSONDecodeError as e:
        print(f"Failed to decode JSON: {e}")
        print(f"Invalid JSON string: {result}")


if __name__ == "__main__":
    # 领域集合
    domainlist = []

    #domain1 = {"name": "市场主体登记", "zntpfile": "市场主体登记指南图谱.xlsx","gztpfile":"市场主体登记规则图谱.xlsx"}
    # domain2 = {"name": "公安户政", "zntpfile": "公安户政指南图谱.xlsx","gztpfile":"公安户政规则图谱.xlsx"}
    # domain3 = {"name": "出入境", "zntpfile": "出入境指南图谱.xlsx","gztpfile":"出入境规则图谱.xlsx"}
    domain4 = {"name": "社保", "zntpfile": "社保指南图谱.xlsx","gztpfile":""}
    # domain5 = {"name": "公积金", "zntpfile": "公积金指南图谱.xlsx","gztpfile":"公积金规则图谱.xlsx"}
    #domain6 = {"name": "人才", "zntpfile": "人才补贴指南图谱2.xlsx", "gztpfile": ""}

    #domainlist.append(domain1)
    # domainlist.append(domain2)
    # domainlist.append(domain3)
    domainlist.append(domain4)
    # domainlist.append(domain5)
    #domainlist.append(domain6)

    issz="" #1:深圳  2：花都   空：公司环境

    if issz=="1":
        # 指南图谱索引
        guideindex = "zntp5"

        # 规则图谱索引
        ruleindex = "gztp5"
    elif issz=="2":
        # 指南图谱索引
        guideindex = "zntp"

        # 规则图谱索引
        ruleindex = "gztp"
    else:
        # 指南图谱索引
        guideindex = "zntp3"

        # 规则图谱索引
        ruleindex = "gztp3"

    #deleteIndex(guideindex,issz)
    #createIndex(guideindex,issz)
    #deleteIndex(ruleindex, issz)
    #createIndex(ruleindex, issz)

    parent_dir = os.path.dirname(os.getcwd())
    file_path = os.path.join(parent_dir, 'data', '111.xlsx')
    # 读取Excel文件
    df = pd.read_excel(file_path, engine='openpyxl', keep_default_na=False)

    for item in domainlist:
        if item["gztpfile"]!='':
            # 规则图谱数据导入
            insertRule(item["name"], item["gztpfile"], ruleindex, issz)
        # 指南图谱数据导入
        insertZntp(item["name"], item["zntpfile"], guideindex, issz,df)

    # 保存修改后的Excel文件
    df.to_excel(file_path, index=False, engine='openpyxl')



