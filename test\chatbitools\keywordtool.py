import requests
import json

def getTextbyinput(inputparams):
	#遍历入参
	fieldname = inputparams["fieldname"]
	fieldvalue = inputparams["fieldvalue"]
	print("fieldvalue" + fieldvalue)
	# 请求参数
	params = {
			"wd": fieldvalue,
			"fields": "content",
			"re_fields": "tablename;tablenamedes;fieldname;fieldnamedes;content;databasesources;tags",
			"cnum": "chatbidata0522002",
			"pn": 0,
			"rn": 1,
			"isSynonym": "1",
			"esdsid": 2,
			"noParticicle": "0",
			"opCondition": [
				{
					"conditionList": [
						{
							"fieldname": fieldname
						}
					],
					"highlights": "false"
				}
			]
		}
	url = "http://192.168.173.99:8915/EpointFrame/rest/esinteligentsearch/getFullTextDataNew"
	try:
		response = requests.post(url, json=params)
		response.raise_for_status()  # 检查请求是否成功
		data = json.loads(response.text)  # 解析JSON响应
		if data:

			# 从内层的JSON中提取records列表
			content = data["content"]
			results=json.loads(content)
			records=results["result"]["records"][0]["content"]
			return records
		return None
	except requests.exceptions.RequestException as e:
		print(f"请求失败: {e}")
		print("请检查网页链接的合法性，确保网址正确无误，然后适当重试访问。")
		return None

inputparams={
      "fieldname": "bdname",
      "fieldnamecn": "标段名称",
      "fieldvalue": "荡口",
      "operation": "="
}
# 调用函数
result = getTextbyinput(inputparams)
# 输出结果
if result:
    print("获取到的内容:", result)
else:
    print("未获取到内容")


