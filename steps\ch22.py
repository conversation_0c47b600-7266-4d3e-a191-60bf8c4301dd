import json
import os

import pandas as pd
import requests

from tools import qwenMax, get_prompt, qwenMax2


def process_excel():
    parent_dir = os.path.dirname(os.getcwd())
    file_path = os.path.join(parent_dir, 'data', '0312.xlsx')
    # 读取Excel文件
    df = pd.read_excel(file_path, engine='openpyxl',keep_default_na=False)
    # 遍历A列，拼接message
    for index, row in df.iterrows():
        # 获取A列的值作为问题
        question = row['三级节点知识']
        if(question):
            messages = [
                {"role": "system", "content": '1、提取问题中的关键词  2、无需分析过程，直接返回结果 3、返回json格式：{关键词:关键词1,关键词2,关键词3}'},
                {"role": "user", "content": question}
            ]
            result = qwenMax(messages)
            try:
                json_str = json.loads(result)
                print(json_str['关键词'])
                df.at[index, '关键词'] = json_str['关键词']
            except json.JSONDecodeError as e:
                print(f"Failed to decode JSON: {e}")
                print(f"Invalid JSON string: {result}")

    # 保存修改后的Excel文件
    df.to_excel(file_path, index=False, engine='openpyxl')




# 总控模块，在拆出业务问题时就拆出多个问题
if __name__ == '__main__':
    process_excel()