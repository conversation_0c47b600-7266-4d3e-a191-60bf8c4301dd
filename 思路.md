## 三种思路：


### JZM 思路
代码combine.ch1.py

问答进入：

并行： 同步思考N个问题，
	1、用户是否需要办理某个事
	2、本次是否需要RAG
	3、是否填表要相关字段

if rag
	rag检索
if 办理事项
	查出该事项需要填报的字段，
    由大模型判断这些字段是否已填报


### hsz 思路
问答进入：
大模型识别表术分类
RAG查询
if  推荐事项
	意图识别，具体某个事项
	跳到办理逻辑


### yx 思路
问答进入：
寒喧模块
意图识别模块
问答办理模块
导服模块
总共模块

每个模块负责规定的输入、输出

设计图：https://www.processon.com/v/675e4abb2b7f8c08fe7f20a5?cid=675d585b6a6b6a4e211d6a11




### 演示路径：
1、纯传统的流程 + kimi graphRAG
2、新流程 + kimi graphRAG
3、新流程 + 新点GraphRAG
