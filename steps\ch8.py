# 将用户的问题，拆分为寒喧、情绪、业务问题

# TODO 调一调提示词

import requests
import json
from tools import get_prompt,qwen7b,qwenMax

# question = "内资企业怎么注销"
# question = "你好"
# question = "你们办理效率太低了"
# question = "你们办理效率太低了，内资企业怎么注销"
question = "去现场办理公司地址变更，是否可以当天就拿到证照？"

messages = [
{
    "role": "system",
    "content": get_prompt("spearate_template.txt",'',[])
}
,{
    "role": "user",
    "content": question
}
]

response = qwenMax(messages)

print(response)

#总控模块，在拆出业务问题时就拆出多个问题