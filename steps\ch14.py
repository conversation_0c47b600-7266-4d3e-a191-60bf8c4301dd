# 将用户的问题，拆分为寒喧、情绪、业务问题

# TODO 调一调提示词

import requests
import json
from tools import get_prompt,qwen7b,qwenMax

# question = "内资企业怎么注销"
# question = "你好"
# question = "你们办理效率太低了"
# question = "你们办理效率太低了，内资企业怎么注销"   
question = "没怎么理解！我是南山区的内资有限责任公司，变更法人，线下办理一定要去南山区吗？"

messages = [
{
    "role": "system",
    "content": get_prompt("test1.txt",'',[])
}
]

response = qwenMax(messages)

print(response)

#总控模块，在拆出业务问题时就拆出多个问题