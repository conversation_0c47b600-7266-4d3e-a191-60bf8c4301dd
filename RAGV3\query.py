#全流程

import json
import os
import sys

import requests

root_path = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
sys.path.append(root_path)
from steps.tools import get_rag3_prompt, qwenMax2

#大模型涉敏判断
def sensitive_word_detection(question):
    messages = [
        {
            "role": "user",
            "content": get_rag3_prompt("ch1.txt", question,'','')
        }
    ]

    response = qwenMax2(messages)
    if "```json" in response:
        response = response.replace("```json","").replace("```","")

    return json.loads(response)['是否涉敏']


#问题拆分
def problem_splitting(question):
    messages = [
        {
            "role": "user",
            "content": get_rag3_prompt("ch2.txt", question,'','')
        }
    ]

    response = qwenMax2(messages)
    if "```json" in response:
        response = response.replace("```json","").replace("```","")

    return json.loads(response)['业务问题'][0]


#领域识别
def identify_areas(question):
    messages = [
        {
            "role": "user",
            "content": get_rag3_prompt("ch3.txt", question,'','')
        }
    ]

    response = qwenMax2(messages)
    if "```json" in response:
        response = response.replace("```json","").replace("```","")

    return json.loads(response)


#提取关键词
def get_keywords(question):
    messages = [
        {
            "role": "user",
            "content": get_rag3_prompt("ch4.txt", question,'','')
        }
    ]

    response = qwenMax2(messages)
    if "```json" in response:
        response = response.replace("```json","").replace("```","")

    return json.loads(response)


#最终润色
def get_result(question,Aresult,Bresult):
    messages = [
        {
            "role": "user",
            "content": get_rag3_prompt("ch5.txt", question,Aresult,Bresult)
        }
    ]

    return qwenMax2(messages)


def callES(question,index_name,select_field,fields,num,condition):
    inteligentsearch_url = "http://192.168.186.65/inteligentsearch/rest"
    params = {
        "wd": question,
        "fields": select_field,
        "re_fields": fields,
        "vecfield": "contentvec",
        "cnums": index_name,
        "pn": 0,
        "rn": num,
        "vecType": "1",
        "pluginName": "EmbeddingOperatorNew",
        "isSynonym": "1",
        "esdsid": 8,
        "accuracy": 15,
        "rescore": "{'inteligentsearchType':'equalratio','sim1':'kworg'}",
        "opCondition": condition,
        "opType": "0"
    }

    print(json.dumps(params,ensure_ascii=False))

    headers = {
        'Content-Type': 'application/json',
        "Authorization": "Bearer " + "token",
        "User-Agent": "python"
    }
    response = requests.post(url=inteligentsearch_url + "/esinteligentsearch/getByVector",
                                data=json.dumps(params), headers=headers)
    re = json.loads(response.text)
    results = eval(json.dumps(json.loads(re["content"])["result"]["records"]))
    return results



def query(question):
    # 大模型涉敏判断
    is_sensitive = sensitive_word_detection(question)

    if is_sensitive == "是":
        print("您的问题超出深小i的解答范围，可以试试问点其他的。")
        exit()
    else:
        #问题的拆分
        rsplit_question = problem_splitting(question)
        if rsplit_question == "":
            print("转彩智")
            exit()
        else:
            #领域识别
            area_json = identify_areas(question)
            area = area_json["领域"]
            is_sz = area_json["是否深圳"]
            area_list = ['市场主体登记','公安户政','出入境','社保','公积金','人才补贴']
            if is_sz == '否':
                print("您好，深小i是深圳AI政务助手，暂无法回答深圳以外的问题。关于您的问题，下面我提供深圳相关情况，供参考。")
                exit()
            else:
                if area in area_list and area != '':
                    #根据问题提取主体、意图、关键词
                    keywords_json = get_keywords(question)
                    keyword = keywords_json["关键词"]
                    body_type = keywords_json["主体类型"]
                    intention = keywords_json["意图"]
                    background = keywords_json["背景"]
                    #拿着领域主体类型、意图、关键词去检索es库

                    #检索es库方法
                    es_results = '没有接入es'
                    #es_results = callES(question, area, "content", "content", 5, "")
                    if len(es_results) == 0:
                        print("转彩智")
                        exit()
                    else:
                        #根据检索结果，构造prompt
                        result = get_result(question,'','')
                        print(result)

if __name__ == '__main__':

    query("内资有限责任公司 转让法人一定要去南山区办理嘛？")
