import torch
from transformers import AutoModelForCausalLM, AutoTokenizer
import time

def load_qwen_model():
    """加载Qwen-1.8B-Chat模型"""
    model_name = "Qwen/Qwen-1_8B-Chat"
    
    print("正在加载Qwen模型...")
    tokenizer = AutoTokenizer.from_pretrained(model_name, trust_remote_code=True)
    model = AutoModelForCausalLM.from_pretrained(
        model_name,
        torch_dtype=torch.float16 if torch.cuda.is_available() else torch.float32,
        device_map="auto" if torch.cuda.is_available() else "cpu",
        trust_remote_code=True
    )
    
    # CPU上使用动态量化
    if not torch.cuda.is_available():
        from torch.quantization import quantize_dynamic
        model = quantize_dynamic(
            model, 
            {torch.nn.Linear}, 
            dtype=torch.qint8
        )
    
    return model, tokenizer

def get_model_size(model):
    """计算模型大小"""
    param_size = 0
    for param in model.parameters():
        param_size += param.nelement() * param.element_size()
    buffer_size = 0
    for buffer in model.buffers():
        buffer_size += buffer.nelement() * buffer.element_size()
    
    size_all_mb = (param_size + buffer_size) / 1024**2
    return size_all_mb

class QwenContextRewriter:
    def __init__(self, model, tokenizer):
        self.model = model
        self.tokenizer = tokenizer
        self.conversation_history = []

    def add_conversation_turn(self, question, answer):
        """添加对话轮次"""
        self.conversation_history.append({
            'question': question,
            'answer': answer
        })

    def clear_history(self):
        """清空对话历史"""
        self.conversation_history = []

    def rewrite_question(self, current_question, max_history=3, debug=False):
        """改写问题"""
        if not self.conversation_history:
            return current_question
        
        recent_history = self.conversation_history[-max_history:]
        
        # 构建上下文
        context_parts = []
        for i, turn in enumerate(recent_history):
            context_parts.append(f"问题{i+1}: {turn['question']}")
            context_parts.append(f"回答{i+1}: {turn['answer']}")
        
        context = "\n".join(context_parts)
        
        # 构建提示词
        prompt = f"""根据以下对话历史，将当前问题改写得更加完整和明确。

对话历史:
{context}

当前问题: {current_question}

请将当前问题改写得更加完整，使其包含必要的上下文信息。只返回改写后的问题，不要其他内容。

改写后的问题:"""

        if debug:
            print(f"提示词:\n{prompt}")
        
        # 使用Qwen的对话格式
        messages = [
            {"role": "user", "content": prompt}
        ]
        
        text = self.tokenizer.apply_chat_template(
            messages,
            tokenize=False,
            add_generation_prompt=True
        )
        
        inputs = self.tokenizer([text], return_tensors="pt")
        
        if debug:
            print(f"输入长度: {inputs['input_ids'].shape}")
        
        with torch.no_grad():
            outputs = self.model.generate(
                inputs['input_ids'],
                max_new_tokens=100,
                temperature=0.3,
                top_p=0.8,
                do_sample=True,
                pad_token_id=self.tokenizer.eos_token_id,
                eos_token_id=self.tokenizer.eos_token_id
            )
        
        # 解码生成的部分
        generated_tokens = outputs[0][inputs['input_ids'].shape[1]:]
        rewritten_question = self.tokenizer.decode(generated_tokens, skip_special_tokens=True).strip()
        
        if debug:
            print(f"生成的内容: '{rewritten_question}'")
        
        # 清理结果
        rewritten_question = rewritten_question.replace("改写后的问题:", "").strip()
        
        # 如果生成结果为空或太短，返回原问题
        if not rewritten_question or len(rewritten_question) < 3:
            return current_question
            
        return rewritten_question

def test_qwen_rewriter():
    """测试Qwen改写器"""
    try:
        model, tokenizer = load_qwen_model()
        print(f"模型大小: {get_model_size(model):.2f} MB")
        
        rewriter = QwenContextRewriter(model, tokenizer)
        
        # 测试1: 政务服务场景
        print("\n=== 测试1: 政务服务场景 ===")
        rewriter.add_conversation_turn(
            "办理营业执照需要什么材料？", 
            "需要身份证、经营场所证明、申请表等材料"
        )
        
        start_time = time.time()
        result1 = rewriter.rewrite_question("去哪里办？", debug=True)
        end_time = time.time()
        
        print(f"原问题: 去哪里办？")
        print(f"改写后: {result1}")
        print(f"耗时: {end_time - start_time:.2f} 秒")
        
        # 测试2: 多轮对话
        print("\n=== 测试2: 多轮对话 ===")
        rewriter.clear_history()
        rewriter.add_conversation_turn("我想开餐厅", "需要办理食品经营许可证")
        rewriter.add_conversation_turn("需要什么材料？", "需要营业执照、健康证、场地证明")
        
        start_time = time.time()
        result2 = rewriter.rewrite_question("多长时间能办好？")
        end_time = time.time()
        
        print(f"原问题: 多长时间能办好？")
        print(f"改写后: {result2}")
        print(f"耗时: {end_time - start_time:.2f} 秒")
        
        # 测试3: 无上下文
        print("\n=== 测试3: 无上下文 ===")
        rewriter.clear_history()
        
        result3 = rewriter.rewrite_question("怎么办理？")
        print(f"原问题: 怎么办理？")
        print(f"改写后: {result3}")
        
    except Exception as e:
        print(f"错误: {e}")
        print("可能需要先安装模型: pip install transformers torch")

if __name__ == "__main__":
    test_qwen_rewriter()
