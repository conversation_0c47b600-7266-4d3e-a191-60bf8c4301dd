#检索图谱
import openpyxl

import requests
import json
import sys
import os

from openai import OpenAI

root_path = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
sys.path.append(root_path)
from steps.tools import get_prompt,qwen7b,qwenMax,callKimi

from combine.ch5 import callKimi
from tqdm import tqdm

from steps.tools import get_prompt16, qwenMax

def sperateQ(question):
    messages = [
    {
        "role": "system",
        "content": get_prompt("ch16.txt",question,[])
    }
    ]

    response = qwen7b(messages)
    if "```json" in response:
        response = response.replace("```json","").replace("```","")
    return json.loads(response)


def callES(question,index_name,select_field,fields,num,condition):
    inteligentsearch_url = "http://**************/inteligentsearch/rest"
    params = {
        "wd": question,
        "fields": select_field,
        "re_fields": fields,
        "vecfield": "contentvec",
        "cnums": index_name,
        "pn": 0,
        "rn": num,
        "vecType": "1",
        "pluginName": "EmbeddingOperatorNew",
        "isSynonym": "1",
        "esdsid": 8,
        "accuracy": 15,
        "rescore": "{'inteligentsearchType':'equalratio','sim1':'kworg'}",
        "opCondition": condition,
        "opType": "0"
    }

    print(json.dumps(params,ensure_ascii=False))

    headers = {
        'Content-Type': 'application/json',
        "Authorization": "Bearer " + "token",
        "User-Agent": "python"
    }
    response = requests.post(url=inteligentsearch_url + "/esinteligentsearch/getByVector",
                                data=json.dumps(params), headers=headers)
    re = json.loads(response.text)
    results = eval(json.dumps(json.loads(re["content"])["result"]["records"]))
    return results

def get_results(data):
    reranker_url = "http://192.168.186.112:8080/epoint-gateway/linezhengshi_reranker/predict"
    headers = {
        'Content-Type': 'application/json',
        "User-Agent": "python",
        "Authorization": "Bearer 4b1f2daed98265cc43aa7ebccfa89f0e",
    }
    response = requests.post(url=reranker_url, data=json.dumps(data), headers=headers, verify=False)

    results = response.text

    return json.loads(results)

def getAllKW(keywords):
    reranker_url = "http://**************/inteligentsearch/rest/esinteligentsearch/getSynonymAndNear"
    headers = {
        'Content-Type': 'application/json',
        "User-Agent": "python",
        "Authorization": "Bearer 4b1f2daed98265cc43aa7ebccfa89f0e",
    }
    data ={
        "wd": keywords,
        "cnums": "nzyxzrgs27",
        "isParticiple": "0",
        "esdsid": 8
    }
    response = requests.post(url=reranker_url, data=json.dumps(data), headers=headers, verify=False)

    results = response.text

    dt = json.loads(json.loads(results)["content"])["result"]["tongyici"]

    all = []
    all = all +  dt.split(',')
    all = all +  keywords.split(' ')
    unique_list = list(dict.fromkeys(all))
    return " ".join(unique_list)

def getAllKW(keywords):
    reranker_url = "http://**************/inteligentsearch/rest/esinteligentsearch/getSynonymAndNear"
    headers = {
        'Content-Type': 'application/json',
        "User-Agent": "python",
        "Authorization": "Bearer 4b1f2daed98265cc43aa7ebccfa89f0e",
    }
    data ={
        "wd": keywords,
        "cnums": "nzyxzrgs27",
        "isParticiple": "0",
        "esdsid": 8
    }
    response = requests.post(url=reranker_url, data=json.dumps(data), headers=headers, verify=False)

    results = response.text

    dt = json.loads(json.loads(results)["content"])["result"]["tongyici"]

    all = []
    all = all +  dt.split(',')
    all = all +  keywords.split(' ')
    unique_list = list(dict.fromkeys(all))
    return " ".join(unique_list)


def query(question,keywords,ptype,purpose):

    #A类问题，图谱入的ES
    index_name = "nzyxzrgsv2_3"  #库名，需要变
    condition = [
            {
                "conditionList": [
                    {
                        "ptype": ptype
                    }
                ],
                "highlights": "false"
            },
            {
                "conditionList": [
                    {
                        "purpose": purpose
                    }
                ],
                "highlights": "false"
            }
        ]    
    result_retrieve1 = callES(keywords, index_name,"kw","ptype;purpose;kw;content",50,condition)

    allkw = getAllKW(keywords)
    
    lst = []
    for item in result_retrieve1:
        lst.append(item["kw"]+"\r\n"+item["content"])

    if len(lst)==0:
        return []
    data_reranker = {"text": question + allkw, "compare_list": list(dict.fromkeys(lst))}
    reranker_results = get_results(data_reranker)

    final_resultsB = []
    for i, re_reranker in enumerate(reranker_results["result"]):
        content, score = re_reranker[0], re_reranker[1]
        item = result_retrieve1[lst.index(content)]
        final_resultsB.append(item)
    dbg =""
    for item in final_resultsB[:10]:
        dbg = dbg + item["ptype"] + "-" + item["purpose"] + "-" + item["kw"] + "\r\n"
    return dbg

#注意，修改上面逻辑后，需同步修改线上的groovy脚本，以供给agent使用
#接口地址：http://192.168.219.30:8080/epoint-blbb-web/frame/pages/apimanage/apiinfo/apimanageinfotab?rowguid=queryAll&serviceid=e35be9f5-0b7b-448a-80f3-a7453381fe21&apitype=FUNCTIONAPI&appguid=null&tableId=queryAll&type=undefined
#test.rest 中可验证测试

def queryTest(question,keywords,ptype,purpose):

    url = "http://192.168.219.30:8080/epoint-blbb-web/rest/dynamicapi/queryA_v2"

    payload =  {
            "question": question,
            "keywords":keywords,
            "ptype":ptype,
            "purpose":purpose,
            "top": 10
        }

    headers = {
        'user-agent': "vscode-restclient",
        'content-type': "application/json",
        'authorization': "Bearer 1ea82c5ae51f46bd41bd8b5224628fcd"
        }

    response = requests.request("POST", url, data=json.dumps(payload), headers=headers)

    return response.json()["dbginfo"]


if __name__ == '__main__':

    question = "内资有限责任公司 转让法人一定要去南山区办理嘛？"
    # question = "内资企业怎么注销"
    # question ="内资有限公司认缴注册资本总额变更登记需要准备什么材料？"
    keywords = sperateQ(question) #+ "  增补换照"
    print(keywords)
    # keywords = "企业迁移 内资有限"
    # print(keywords)
    # keywords = "内资有限责任公司 备案 监事"
    # print(keywords)

    ptype = keywords["主体类型"]
    purpose = keywords["意图"]
    keywords = "办理地址"#keywords["关键词"]

    content = queryTest(question,keywords,ptype,purpose)

    print(content)

