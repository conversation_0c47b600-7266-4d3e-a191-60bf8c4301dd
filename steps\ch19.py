from pydantic import json
from tencentcloud.common import credential
from tencentcloud.common.exception.tencent_cloud_sdk_exception import TencentCloudSDKException
from tencentcloud.common.profile.client_profile import ClientProfile
from tencentcloud.hunyuan.v20230901 import hunyuan_client, models

def call_tx_hunyuan(args):

    stream = args.get("stream", False)
    messages = args.get("messages", None)
    model = args.get("choose_model", "hunyuan-standard")
    temperature = float(args.get("temperature", 0.8))
    secret_id = "AKID1tY0tdEk260lXAct8FBxa9VejdLq8E9u"
    secret_key = "f9RXQ8f195nHWaWH6rSt57wXu3zhDVnw"

    if messages is None:
        return "请输入问题"
    try:
        # 实例化一个认证对象，入参需要传入腾讯云账户secretId，secretKey
        cred = credential.Credential(secret_id, secret_key)
        cpf = ClientProfile()
        # 预先建立连接可以降低访问延迟
        cpf.httpProfile.pre_conn_pool_size = 3
        client = hunyuan_client.HunyuanClient(cred, "ap-guangzhou", cpf)
        req = models.ChatCompletionsRequest()
        p = {
            "Messages": [{"Role": mess["role"], "Content": mess["content"]} for mess in messages if
                         mess["content"] != ""],
            "Model": model,
            "Stream": stream,
            "Temperature": temperature
        }
        # print(p)
        resp = client._call_and_deserialize("ChatCompletions", p, models.ChatCompletionsResponse, headers=req.headers)
        if stream:  # stream 示例
            def stream_callback():
                for event in resp:
                    data = json.loads(event['data'])
                    for choice in data['Choices']:
                        yield choice['Delta']['Content']

            return stream_callback()
        else:  # 非 stream 示例
            # 通过 Stream=False 参数来指定非 stream 协议, 一次性拿到结果
            return resp.Choices[0].Message.Content

    except TencentCloudSDKException as err:
        print(err)
