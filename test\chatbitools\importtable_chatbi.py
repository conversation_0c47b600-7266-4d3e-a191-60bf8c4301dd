# -*- coding: utf-8 -*-
"""
@Des     :
1.原始数据导入索引：data0522
2.表结构数据导入索引：table0522
"""
import concurrent
import datetime
import re
from uuid import uuid4
import json
import sys
import os
import pandas as pd
import requests
from requests.models import HTTPBasicAuth

def createIndex(index_name,type,esdsid):
        #表结构
    if type==1:
        path = "table0522.json"
        #基础数据
    else:
        path = "data0522.json"
    inteligentsearch_url = "http://192.168.173.99:8915/EpointFrame/rest"


    init_data = json.load(open(path, encoding="utf-8"))
    init_data["esdsid"] = esdsid
    init_data["category"]["categoryName"] = index_name
    init_data["category"]["categoryDescribe"] = index_name
    init_data["category"]["categoryNum"] = index_name

    init_data['category'] = json.dumps(init_data['category'])
    build_url = inteligentsearch_url + "/esinteligentsearchmanageinit/addCategory"
    headers = {
        'Content-Type': 'application/json',
        "Authorization": "Bearer " + "token",
        "User-Agent": "python"
    }

    response = requests.post(url=build_url, json=init_data, headers=headers)
    print(response.text)


def deleteIndex(index_name):
    esdsid=2
    inteligentsearch_url = "http://192.168.173.99:8915/EpointFrame/rest"

    init_data = {}
    init_data["categorynum"] = index_name
    init_data["esdsid"] = esdsid
    build_url = inteligentsearch_url + "/esinteligentsearchmanageinit/deleteCategoryByNum"
    headers = {
        'Content-Type': 'application/json',
        "Authorization": "Bearer " + "token",
        "User-Agent": "python"
    }

    response = requests.post(url=build_url, json=init_data, headers=headers)
    print(response.text)


def embding(q_list):

    embed_url = "http://192.168.173.99:5007/get_embedding/embed"

    data_embed = {"text": q_list}
    headers = {
        'Content-Type': 'application/json',
        "User-Agent": "python",
        "Authorization": "Bearer 4b1f2daed98265cc43aa7ebccfa89f0e",
    }
    response = requests.post(url=embed_url, data=json.dumps(data_embed), headers=headers)
    results = response.text
    return json.loads(results)["result"]


def  insertData(databasesources,index_name, tablename, tablenamedes, fieldname, fieldnamedes, order, tags):

    es_url = "https://192.168.173.99:9200"

    id = str(uuid4())

    time_now = datetime.datetime.now().strftime('%Y-%m-%d')
    situation = ""

    data = {
        "tablename_keyword": tablename,
        "tablenamedes_text": tablenamedes,
        "tablenamedesvec_vector": embding([tablenamedes])[0],

        "fieldname_keyword": fieldname,
        "fieldnamedes_text": fieldnamedes,
        "fieldnamedesvec_vector": embding([fieldnamedes])[0],
        "tags_keyword": tags,
        "order_keyword": order,
        "databasesources_keyword": databasesources,

        "id_keyword": id,
        "infodate_date": time_now,
        "syscategory_keyword": index_name
    }
    data = json.dumps(data)

    headers = {'Content-Type': 'application/json'}
    insert_url = es_url + "/" + index_name + "/_doc" + "/"
    response = requests.put(insert_url + id, headers=headers, auth=HTTPBasicAuth("admin", "admin"),
                            verify=False, data=data.encode('utf-8'))
    try:
        re = json.loads(response.text)
        if "error" in re:
            error = re["error"]["type"]
            print(re)
            print(f"[*] insert应用中，导入数据失败，请检查传参，错误类型{error}")
            print(f"[*] 错误数据{data}")
        else:
            # pass
            result = re["result"]
            #print(f"[*] 导入数据成功，主键：{id}，状态：{result}")
    except Exception as e:
        print(f"[*] insert应用中， 导入数据出错，请检查es地址是否访问通，错误类型{e}，es接口返回{response.text}")
        print(f"[*] 错误数据{data}")

def  insertJCSJDC(databasesources, index_name, tablename,  fieldname,tablenamedes,  fieldnamedes, content, order, tags):

    es_url = "https://192.168.173.99:9200"

    id = str(uuid4())

    time_now = datetime.datetime.now().strftime('%Y-%m-%d')


    data = {
        "tablename_keyword": tablename,
        "fieldname_keyword": fieldname,
        "tablenamedes_text": tablenamedes,
        "fieldnamedes_text": fieldnamedes,
        "content_text": content,
        "tags_keyword": tags,
        "order_keyword": order,
        "databasesources_keyword": databasesources,

        "id_keyword": id,
        "infodate_date": time_now,
        "syscategory_keyword": index_name
    }
    data = json.dumps(data)

    headers = {'Content-Type': 'application/json'}
    insert_url = es_url + "/" + index_name + "/_doc" + "/"
    response = requests.put(insert_url + id, headers=headers, auth=HTTPBasicAuth("admin", "admin"),
                            verify=False, data=data.encode('utf-8'))
    try:
        re = json.loads(response.text)
        if "error" in re:
            error = re["error"]["type"]
            print(re)
            print(f"[*] insert应用中，导入数据失败，请检查传参，错误类型{error}")
            print(f"[*] 错误数据{data}")
        else:
            # pass
            result = re["result"]
            #print(f"[*] 导入数据成功，主键：{id}，状态：{result}")
    except Exception as e:
        print(f"[*] insert应用中， 导入数据出错，请检查es地址是否访问通，错误类型{e}，es接口返回{response.text}")
        print(f"[*] 错误数据{data}")


def parseRuleExcel(filename):
    file_path = filename
    result = []
    df = pd.read_excel(str(file_path), keep_default_na=False)
    order=""
    tags=""
    for index, row in df.iterrows():

        databasesources = ""
        tablename = ""
        tablenamedes = ""
        fieldname=""
        fieldnamedes=""
        if str(row["数据库来源"]) != '':
            databasesources = str(row["数据库来源"])
        if str(row["表名"]) != '':
            tablename = str(row["表名"])
        if str(row["表诠释"]) != '':
            tablenamedes = str(row["表诠释"])
        if str(row["字段名"]) != '':
            fieldname = str(row["字段名"])
        if str(row["字段诠释"]) != '':
            fieldnamedes = str(row["字段诠释"])

        if "排序号" in row:
            order = row["排序号"]
        else:
            order = ""
        if "关键词标签" in row:
            tags = row["关键词标签"]
        else:
            tags = ""
        result.append({
            "databasesources": databasesources,
            "tablename": tablename,
            "tablenamedes": tablenamedes,
            "fieldname": fieldname,
            "fieldnamedes": fieldnamedes,
            "order":order,
            "tags": tags
        })
    return result


def parseGraphexcel(filename):
    #excel直接转

    file_path = filename
    result = []
    df = pd.read_excel(str(file_path), keep_default_na=False)
    order = ""
    tags = ""
    for index, row in df.iterrows():
        databasesources = ""
        tablename = ""
        fieldname = ""
        content = ""
        if str(row["数据库来源"]) != '':
            databasesources = str(row["数据库来源"])
        if str(row["表名"]) != '':
            tablename = str(row["表名"])
        if str(row["字段名"]) != '':
            fieldname = str(row["字段名"])
        if str(row["表名诠释"]) != '':
                tablenamedes = str(row["表名诠释"])
        if str(row["字段名诠释"]) != '':
                fieldnamedes = str(row["字段名诠释"])
        if str(row["内容"]) != '':
            content = str(row["内容"])

        if "排序号" in row:
            order = row["排序号"]
        else:
            order = ""
        if "关键词标签" in row:
            tags = row["关键词标签"]
        else:
            tags = ""
        result.append({
            "databasesources": databasesources,
            "tablename": tablename,
            "fieldname": fieldname,
            "tablenamedes": tablenamedes,
            "fieldnamedes": fieldnamedes,
            "content": content,
            "order": order,
            "tags": tags
        })
    return result


def insertjcsj(index_name):
    #插入基础数据
    filename="基础数据0522002.xlsx"
    questionlist = parseGraphexcel(filename)
    for object in questionlist:
        try:
            databasesources = object["databasesources"]
            tablename = object["tablename"]
            fieldname = object["fieldname"]
            tablenamedes = object["tablenamedes"]
            fieldnamedes = object["fieldnamedes"]
            content = object["content"]
            order = str(object["order"])
            tags = object["tags"]

            insertJCSJDC(databasesources, index_name, tablename,  fieldname,tablenamedes,  fieldnamedes, content, order, tags)
        except Exception as e:
            print(e.args)
            print(f"[*] 插入数据错误，错误数据{object}")


def insertRule(index_name):
    filename="表结构数据.xlsx"
    lst = parseRuleExcel(filename)
    for object in lst:
        try:
            databasesources = object["databasesources"]
            tablename = object["tablename"]
            tablenamedes = object["tablenamedes"]
            fieldname = object["fieldname"]
            fieldnamedes =  object["fieldnamedes"]
            order = str(object["order"])
            tags = object["tags"]

            insertData(databasesources,index_name, tablename, tablenamedes, fieldname, fieldnamedes, order, tags)
        except Exception as e:
            print(e.args)
            print(f"[*] 插入数据错误，错误数据{object}")

def insertJCSJDC_threaded(databasesources, index_name, tablename, fieldname, tablenamedes, fieldnamedes, content, order, tags):
    es_url = "https://192.168.173.99:9200"

    id = str(uuid4())
    time_now = datetime.datetime.now().strftime('%Y-%m-%d')

    data = {
        "tablename_keyword": tablename,
        "fieldname_keyword": fieldname,
        "tablenamedes_text": tablenamedes,
        "fieldnamedes_text": fieldnamedes,
        "content_text": content,
        "tags_keyword": tags,
        "order_keyword": order,
        "databasesources_keyword": databasesources,
        "id_keyword": id,
        "infodate_date": time_now,
        "syscategory_keyword": index_name
    }
    data = json.dumps(data)

    headers = {'Content-Type': 'application/json'}
    insert_url = es_url + "/" + index_name + "/_doc" + "/"
    response = requests.put(insert_url + id, headers=headers, auth=HTTPBasicAuth("admin", "admin"),
                            verify=False, data=data.encode('utf-8'))
    try:
        re = json.loads(response.text)
        if "error" in re:
            error = re["error"]["type"]
            print(re)
            print(f"[*] insert应用中，导入数据失败，请检查传参，错误类型{error}")
            print(f"[*] 错误数据{data}")
        else:
            result = re["result"]
            print(f"[*] 导入数据成功，主键：{id}，状态：{result}")
    except Exception as e:
        print(f"[*] insert应用中， 导入数据出错，请检查es地址是否访问通，错误类型{e}，es接口返回{response.text}")
        print(f"[*] 错误数据{data}")


def insertjcsj_threaded(index_name):
    # 插入基础数据
    filename = "基础数据0522002.xlsx"
    questionlist = parseGraphexcel(filename)
    with concurrent.futures.ThreadPoolExecutor(max_workers=10) as executor:
        futures = []
        for object in questionlist:
            try:
                databasesources = object["databasesources"]
                tablename = object["tablename"]
                fieldname = object["fieldname"]
                tablenamedes = object["tablenamedes"]
                fieldnamedes = object["fieldnamedes"]
                content = object["content"]
                order = str(object["order"])
                tags = object["tags"]
                futures.append(executor.submit(insertJCSJDC_threaded, databasesources, index_name, tablename, fieldname, tablenamedes, fieldnamedes, content, order, tags))
            except Exception as e:
                print(e.args)
                print(f"[*] 插入数据错误，错误数据{object}")
        for future in concurrent.futures.as_completed(futures):
            try:
                future.result()
            except Exception as e:
                print(f"[*] 线程执行出错：{e}")

if __name__ == "__main__":
    # 指南图谱索引
    dataindex = "chatbidata0522002"

    # 规则图谱索引
    tableindex = "chatbitable0522002"


    #deleteIndex(guideindex)
    #表结构
    createIndex(tableindex,1,2)

    insertRule(tableindex)
    #基础数据
    createIndex(dataindex, 2, 2)
    insertjcsj(dataindex)
    print("-------导入数据结束--------")





