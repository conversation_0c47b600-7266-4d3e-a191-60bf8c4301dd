@Grab('org.apache.groovy:groovy-json:4.0.0')

import groovy.json.JsonSlurper
import groovy.json.JsonOutput
import groovy.json.JsonBuilder
import java.util.regex.Pattern

def main(metrics) {
	// 当前时间
	def currentDate = new Date().format('yyyy-MM-dd')
	// 解析指标结果
	def configErrorMsg = ""
	if(!metrics){
		configErrorMsg = "转换指标失败"
	}
	
	// 先转换为JSON字符串
	def jsonString = JsonOutput.prettyPrint(JsonOutput.toJson(metrics))
	
	// 将Unicode编码转换回中文字符
	def unescapedJson = unescapeUnicode(jsonString)

	return [
		errormsg : configErrorMsg,
		metrics : unescapedJson,
		datetime : currentDate
	]
}

// 将Unicode编码转换为中文字符的方法
def unescapeUnicode(String str) {
	def pattern = Pattern.compile("\\\\u([0-9a-fA-F]{4})")
	def matcher = pattern.matcher(str)
	def result = new StringBuffer()
	
	while (matcher.find()) {
		def unicode = matcher.group(1)
		def ch = (char) Integer.parseInt(unicode, 16)
		matcher.appendReplacement(result, String.valueOf(ch))
	}
	matcher.appendTail(result)
	
	return result.toString()
}

// 测试数据
def testData = [
    [
        terms: [
            [
                name: "前年",
                description: "意思是指去年前边的那一年。\n如本年是2025年，去年是2024年，前年是2023年"
            ],
            [
                name: "近3年", 
                description: "近3年代表包含今年在内的3个年份。"
            ]
        ]
    ]
]

// 执行测试
def result = main(testData)
println "错误信息: ${result.errormsg}"
println "日期时间: ${result.datetime}"
println "指标数据:"
println result.metrics
