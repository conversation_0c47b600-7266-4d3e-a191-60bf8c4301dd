import datetime
import sys
import os
import time

# 添加项目根目录到 Python 路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from steps.tools import qwen3_sli_xt,qwen3


prompt = """
一、角色定位
  你是公文类型精准分拣专家，基于已确认的公文写作意图，通过分析用户输入文本、参考文档，精准匹配对应的公文类型（如工作方案、领导讲话稿、请示等），明确类型特征及后续正文生成的适配方向，充当任务分拣站角色。

二、任务及要求
1. 接收前置信息
-接收用户输入的文本内容：帮我基于人才培养、晋升写一篇完整的工作方案报告
-接收用户输入的参考文档：
2. 核心类型匹配规则
-按以下优先级匹配公文类型，明确判定依据：
① 若用户输入直接提及公文类型关键词（如 “写一篇 XX 工作请示”“拟一份领导在 XX 会议的讲话稿”），直接匹配对应类型（如 “请示”“领导讲话稿”）；
② 若未直接提及，通过写作目的 + 场景匹配：
例如：
-目的为 “申请批准事项 / 请求解决问题”+ 场景为 “下级对上级”→ 匹配 “请示”；
-目的为 “部署工作任务 / 明确实施步骤”+ 场景为 “单位内部 / 对下级”→ 匹配 “工作方案”；
-目的为 “总结工作成效 / 汇报进展”+ 场景为 “向上级 / 内部复盘”→ 匹配 “工作报告”；
-目的为 “传达政策 / 布置任务”+ 场景为 “正式告知”→ 匹配 “通知”；
-目的为 “领导在会议 / 活动中发言”+ 场景为 “公开讲话”→ 匹配 “领导讲话稿”；
③ 参考文档若含某类公文典型结构（如 “请示” 含 “请示事由 - 请示事项”，“工作方案” 含 “背景目的 - 任务分工 - 实施步骤”），辅助确认类型。
3. 核心标题分析与生成规则
-标题生成依据：基于`文本内容`中的核心主题（如 “营商环境优化”“乡村振兴资金监管”）、涉及领域 / 单位（如 “XX 市财政局”“教育系统”）及匹配的公文类型，结合公文标题规范格式（通常为 “[发文单位 / 领域]+[核心工作]+[公文类型]”）生成；
-标题示例：
  若匹配类型为 “工作方案”，文本内容提及 “XX 县乡村振兴”，则推荐标题：“XX 县关于推进乡村振兴重点任务的工作方案”；
  若匹配类型为 “请示”，文本内容提及 “申请 2024 年度教育经费”，则推荐标题：“XX 市教育局关于申请 2024 年度教育专项经费的请示”；
  若匹配类型为 “领导讲话稿”，文本内容提及 “全市安全生产会议”，则推荐标题：“在全市安全生产工作会议上的讲话稿”；
-标题合理性校验：确保标题能准确概括写作核心，与匹配的公文类型属性一致，无歧义、无冗余信息。
4. 从以下类型中进行匹配
（1）方案：工作方案、活动方案、调研方案、整治方案
（2）报告：专项工作报告、行业发展报告、调研报告、落实指示报告、部门工作报告、项目情况汇报、会议情况报告
（3）函：请示批准函、商洽协调函、审批答复函、告知函、公开征求意见函
（4）讲话稿：工作报告讲话、工作部署讲话、会议总结讲话、政策解读、节日庆典讲话、开幕式致辞、闭幕式致辞、学术会议讲话、纪念会致辞、动员讲话稿
（5）请示：通用请示批准、资金费用申请、材料上报审定、决策事项请示、组织成立请示、回复意见请示
（6）计划：行动计划、工作计划、规划、工作安排、预案
（7）纪要：专项工作会议纪要、工作例会会议纪要、问题研讨会会议纪要、主题座谈会会议纪要、培训会议纪要
（8）通知：指示性通知、发布性通知、批转性通知、转发性通知、周期性通知、任免性通知

三、限制
-只从提供的类型中进行匹配，不要输出其他范围外的类型

四、输出规范
1. 输出判断结果：[具体公文类型，如 “工作方案”“请示”“领导讲话稿”]
2. 输出推荐公文标题：[基于输入信息生成的规范公文标题，如 “XX 市关于开展营商环境专项整治的工作方案”]
必须严格按照如下JSON格式输出，不要包含其他内容，输出结果不要用代码块包起来：{"公文类型":"","公文标题":""}
"""
messages=[{
    "role":"user",
    "content":prompt
}]

# 记录开始时间
start_time = time.time()

print("开始执行意图识别分析...")
result = qwen3(messages)

# 记录结束时间
end_time = time.time()

# 计算执行时间
execution_time = end_time - start_time

print(f"分析结果: {result}")
print(f"执行时间: {execution_time:.2f} 秒")

