import torch
from transformers import AutoModelForSeq2SeqLM, AutoTokenizer
from torch.quantization import quantize_dynamic
import time

def load_flan_t5_int8():
    """
    加载Flan-T5-Base模型并进行int8量化
    """
    print("正在加载Flan-T5-Base模型...")

    # 加载原始模型
    model = AutoModelForSeq2SeqLM.from_pretrained("google/flan-t5-base")
    tokenizer = AutoTokenizer.from_pretrained("google/flan-t5-base")

    print("原始模型加载完成，开始int8量化...")

    # 进行动态量化
    model_int8 = quantize_dynamic(
        model,
        {torch.nn.Linear},
        dtype=torch.qint8
    )

    print("int8量化完成！")

    return model_int8, tokenizer

def get_model_size(model):
    """
    计算模型大小（MB）
    """
    param_size = 0
    for param in model.parameters():
        param_size += param.nelement() * param.element_size()
    buffer_size = 0
    for buffer in model.buffers():
        buffer_size += buffer.nelement() * buffer.element_size()

    size_all_mb = (param_size + buffer_size) / 1024**2
    return size_all_mb

def test_model_inference(model, tokenizer, test_text="Translate to German: Hello, how are you?"):
    """
    测试模型推理性能
    """
    print(f"\n测试输入: {test_text}")

    # 编码输入
    inputs = tokenizer(test_text, return_tensors="pt")

    # 推理计时
    start_time = time.time()
    with torch.no_grad():
        outputs = model.generate(**inputs, max_length=50)
    end_time = time.time()

    # 解码输出
    result = tokenizer.decode(outputs[0], skip_special_tokens=True)
    inference_time = end_time - start_time

    print(f"输出结果: {result}")
    print(f"推理时间: {inference_time:.3f}秒")

    return result, inference_time

def main():
    """
    主函数：演示Flan-T5-Base的int8量化
    """
    try:
        # 加载量化模型
        model_int8, tokenizer = load_flan_t5_int8()

        # 计算模型大小
        model_size = get_model_size(model_int8)
        print(f"\n量化后模型大小: {model_size:.2f} MB")

        # 测试推理
        test_cases = [
            "Translate to German: Hello, how are you?",
            "Summarize: The quick brown fox jumps over the lazy dog.",
            "Answer: What is the capital of France?"
        ]

        print("\n=== 模型推理测试 ===")
        for i, test_text in enumerate(test_cases, 1):
            print(f"\n测试 {i}:")
            test_model_inference(model_int8, tokenizer, test_text)

        return model_int8, tokenizer

    except Exception as e:
        print(f"错误: {e}")
        return None, None

if __name__ == "__main__":
    model_int8, tokenizer = main()
