import base64
import json

import pandas as pd
import requests
import os
from tools import get_prompt,qwenMax2
#【深圳边聊边办】领域-主体类型-意图--情形提取测试

#领域识别
#def getDomain():

#主体类型、一级意图情形识别
def getPrincipaltype(question):
    prompt=""
    messages = [
        {"role": "user","content": prompt}
    ]
    data = {
        "messages": messages,
        "stream": False
    }
    response = qwenMax2(data)
    return json.loads(response['result']['content'])

#意图情形识别
def getIntensionAndCondition(question):
    intentarray = []
    conditionarray = []
    entityarray=[]
    subjecttype = ""
    intent = ""
    condition = ""

    #获取意图情形列表
    firsttypelist = getFisrtType()
    #获取主体类型
    datalist = getDetailType("1","")
    for item in datalist["entity_type"]:
        entityarray.append(item["entity_type_name"])

    for item in firsttypelist["klcategory_intent"]:
        intentarray.append(item["category_name"])

    for item in firsttypelist["klcategory_condition"]:
        conditionarray.append(item["category_name"])

    prompt = get_prompt("questionsplit_ztytqx2.txt",question,str(entityarray),str(intentarray),str(conditionarray))
    messages = [
        {"role": "user", "content": prompt}
    ]
    firstcatagoryobj = qwenMax2(messages)
    content_data = json.loads(firstcatagoryobj)
    subjecttype=content_data.get('主体类型')
    firstcondition = content_data.get('情形')
    firstintent = content_data.get('意图')

    intentarray = []

    if firstintent != "":
        jsondata = getDetailType("2", firstintent)
        for item in jsondata["intent"]:
            intentarray.append(item["intent_name"])

    prompt = get_prompt("questionsplit_ytqx.txt", question,"", str(intentarray), "")
    messages = [
        {"role": "user", "content": prompt}
    ]
    secondcatagoryobj = qwenMax2(messages)
    response = json.loads(secondcatagoryobj)
    intent = response.get('意图')

    resultobj={}
    resultobj["主体类型"] = subjecttype
    resultobj["意图"] = intent
    resultobj["情形"] = firstcondition
    return resultobj


#获取一级分类
def getFisrtType():
    url="http://192.168.219.27:8080/qy-epoint-web/rest/klknowledgerest/getKlCategoryDataList"
    params={"privatekey":"1","category_type":"2,3","domain_id":"008d3fef-da26-11ef-9151-08bfb8b9824c"}
    headers = {
        'Content-Type': 'application/json'
    }

    response = requests.post(url, json=params, headers=headers)
    return json.loads(response.text)

#获取一级分类下的意图和情形
def getDetailType(category_type,category_name):
    url="http://192.168.219.27:8080/qy-epoint-web/rest/klknowledgerest/getKlDataList"
    params={"privatekey":"1","category_name":category_name,"category_type":category_type,"domain_id":"008d3fef-da26-11ef-9151-08bfb8b9824c"}
    headers = {
        'Content-Type': 'application/json'
    }

    response = requests.post(url, json=params, headers=headers)
    return json.loads(response.text)

def process_excel():
    parent_dir = os.path.dirname(os.getcwd())
    file_path = os.path.join(parent_dir, 'data', '业务领域微领域提取测试.xlsx')
    # 读取Excel文件
    df = pd.read_excel(file_path, engine='openpyxl',keep_default_na=False)
    # 遍历A列，拼接message
    for index, row in df.iterrows():
        # 获取A列的值作为问题
        question = row['原始问题']
        if(question):
            result = getIntensionAndCondition(question)
            print(result)
            df.at[index, '主体'] = result['主体类型']
            df.at[index, '意图'] = result['意图']
            df.at[index, '情形'] =result['情形']
    # 保存修改后的Excel文件
    df.to_excel(file_path, index=False, engine='openpyxl')

if __name__ == '__main__':
    process_excel()
