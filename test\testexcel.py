import pandas as pd
import requests
from requests.auth import HTTPBasicAuth

# 配置接口信息
url = "http://127.0.0.1:5000/api/tablenamesearch"
auth = HTTPBasicAuth('admin', 'admin')
headers = {
    "Content-Type": "application/json"
}

# Excel 文件路径
input_file = "问题集.xlsx"
output_file = "questions_with_results.xlsx"

# 读取问题列
df = pd.read_excel(input_file)
results = []
tables = []

for idx, row in df.iterrows():
    question = row.get("问题")
    if not isinstance(question, str) or not question.strip():
        results.append("无效问题")
        tables.append("")
        continue

    payload = {"question": question.strip(),"top":1}
    try:
        response = requests.post(url, json=payload, headers=headers, auth=auth)
        response.raise_for_status()
        data = response.json()

        # 添加完整返回内容
        results.append(str(data))
        # 按照新的出参格式提取表名
        result_list = data.get("result", [])
        if result_list and isinstance(result_list, list):
            table_str = ",".join([item.get("tablename", "") for item in result_list if item.get("tablename")])
        else:
            table_str = ""
        tables.append(table_str)

        print(f"[✓] {question} -> 表名: {table_str}")
    except Exception as e:
        error_msg = f"请求失败: {e}"
        results.append(error_msg)
        tables.append("")
        print(f"[x] {question} -> {error_msg}")

# 写入新列
df["表名"] = tables
df["明细"] = results
df = df[["问题", "表名", "明细"]]  # 重排序列

# 保存结果
df.to_excel(output_file, index=False)
print(f"✅ 处理完成，结果已保存至：{output_file}")
