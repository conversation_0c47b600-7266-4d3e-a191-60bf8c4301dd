## 在ch2基础上，增加问题与答案相关性判断


# 测试数据：
# 我觉得政府办事效率很高，但有的事项最好能再快一些，我帮朋友咨询一下办理律师注销后重新执业需要多长审批时间？
# 您好！我是广西户籍，法学本科生，2020年通过法考，2022年取得中国律师执业证，2024年12月注销了律师证，现在公司做法务。我的配偶是香港永久居民，我现在拟通过结婚方式赴港定居取得香港身份。请问，如果我取得了香港身份证，我还可以在深圳申请恢复律师执业吗？
# 我今年40岁，是壮族，2016年自己考过了国家司法考试，不是法院的退休人员。 请问申请首次注册需要提交什么材料？
# 第一个问题是我来自广西，想咨询一下办理律师执业变更需要满足什么条件？还有一个问题是我表弟是香港公民，他如果要来深圳进行律师首次执业注册需要提交什么材料？
# 本人户籍所在地是贵州，本人于2022年4月在贵州省通过参加法考获得法律职业资格证书C证，现本人想在深圳市注册的律师事务所申请实习律师，请问获得法律职业资格证书C证能否在深圳市的律师事务所申请实习律师。我想确认一下非深圳户籍人员持有法律职业资格证书C证能否在深圳市的律师事务所申请实习律师？另外，能否告诉我申请首次注册需要提交什么材料？


import json
import os
import re
import sys

import requests
from openai import OpenAI

root_path = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
sys.path.append(root_path)

from steps.tools import get_prompt

client = OpenAI(
    api_key = "sk-NOOv7U6XgWdCwLmkqVbnC14YIJjvzR1ZQNWezNJEC9L29j3S", # 在这里将 MOONSHOT_API_KEY 替换为你从 Kimi 开放平台申请的 API Key
    base_url = "https://api.moonshot.cn/v1",
)
messages = [
	{"role": "system", "content": "请不要查询任何联网信息，也不需要调用大模型自己已有的知识储备，你现是为位政务服务中心的工作人员，负责处理用户的问题。禁止做出任何的假设和推测，要严格根据问题进行回答和反问政务服务中心代表着政府，做事必须有依有据，不能随意根据自己的经验来。"},
]

def callKimi(input: str) -> str:
    # 我们将用户最新的问题构造成一个 message（role=user），并添加到 messages 的尾部
    messages.append({
        "role": "user",
        "content": input,
    })

    # 携带 messages 与 Kimi 大模型对话
    completion = client.chat.completions.create(
        model="moonshot-v1-8k",
        messages=messages,
        temperature=0,
    )

    # 通过 API 我们获得了 Kimi 大模型给予我们的回复消息（role=assistant）
    assistant_message = completion.choices[0].message

    # 为了让 Kimi 大模型拥有完整的记忆，我们必须将 Kimi 大模型返回给我们的消息也添加到 messages 中
    messages.append(assistant_message)

    return assistant_message.content

def emocheck(query,chat_history):
    response = callKimi(get_prompt("emocheck_template.txt",query,[]))
    if response != "" and response[0] =='2':
        return 2,""
    elif response != "" and response[0] =='3':
        return 3,""
    elif response != "" and response[0] =='0':
        return 0,response[2:]
    elif response != "" and response[0] =='1':
        return 1,response[2:]
    else:
        return "", response

def getKeywords(query,chat_history):
    for i in range(5):
        try:
            response = callKimi(get_prompt("getkeywords_template.txt",query,[]))
            json_data = json.loads(response)
            return json_data["关键词"]
        except:
            continue
    return response

def queryGraph(keywords):
    url = "http://127.0.0.1:5000/querygraph"

    payload = "{\"keywords\":\"" + keywords +"\",\"graphurl\":\"http://**************:40436/relation_nzyxzrgs/gremlin\",\"index_name\":\"nzyxzrgs\"}"
    headers = {
        'user-agent': "vscode-restclient",
        'content-type': "application/json"
        }

    response = requests.request("POST", url, data=payload, headers=headers)

    finialResult = response.json()["finalResult"]
    if response.json()["graphlist"] != '':
        resultSet = set()
        pattern = re.compile(r'\[(.*?)\]')
        # 获取集合中的[]内的节点id
        for item in response.json()["graphlist"]:
            result = pattern.findall(item)
            if result:
                for codeId in result:
                    fibonacci(codeId, resultSet)

        if resultSet:

            for id in resultSet:
                # 先查询当前节点的name
                name = findEntityName(id)
                name = getTotalName(id, name)
                resultStr = "[" + name + "]" + "(" + findEntityDesc(id) + ")\n"

                finialResult += resultStr
    return finialResult


def fibonacci(id,resultSet):
    result = findParentEntity(id)
    # print(f"父节点信息：{result}")
    if result :
        for item in result:
            if item["id"] is not None and item["id"] != "":
                resultSet.add(item["id"])
                fibonacci(item["id"], resultSet)

def getTotalName(id,name):
    result = findParentEntity(id)
    # print(f"父节点信息：{result}, 当前ID: {id}, 当前名称: {name}")  # 添加调试信息
    if result :
        if name != "":
            name = result[0]["name"] + "-" + name
        return getTotalName(result[0]["id"], name)
    return name

def findParentEntity(id):
    url = "http://**************:40436/relation_nzyxzrgs/gremlin"
    params = {"params": {"gremlin": "g.V("+str(id)+").in().toList()"}}
    response = requests.request("POST", url, data=json.dumps(params))
    dt = response.json()

    entitylist=[]
    for item in dt["result"]["data"]:
        entity={}
        entity["id"] = item["id"]
        entity["name"] = item["properties"]["name"][0]["value"]
        entitylist.append(entity)
    return entitylist

def findEntityName(id):
    url = "http://**************:40436/relation_nzyxzrgs/gremlin"
    # params = {"params": {"gremlin": "g.V("+id+")"}}
    params = {"params": {"gremlin": "g.V("+str(id)+")"}}
    response = requests.request("POST", url, data=json.dumps(params))
    dt = response.json()
    description = dt["result"]["data"][0]["properties"]["name"][0]["value"]
    return description

def findEntityDesc(id):
    url = "http://**************:40436/relation_nzyxzrgs/gremlin"
    # params = {"params": {"gremlin": "g.V("+id+")"}}
    params = {"params": {"gremlin": "g.V("+str(id)+")"}}
    response = requests.request("POST", url, data=json.dumps(params))
    dt = response.json()
    description = ""
    if dt["result"]["data"][0]["properties"].get("description") is not None:
        description = dt["result"]["data"][0]["properties"]["description"][0]["value"]
    return description

def answerbyGraph(query,graphcontent,chat_history):
    prompt = get_prompt("answer_template.txt",query,[])
    prompt = prompt.replace("{graphcontent}", graphcontent)
    response = callKimi(prompt)
    return response

def checkGraph(query,graphcontent):
    prompt = get_prompt("check_template.txt", query, [])
    prompt = prompt.replace("{graphcontent}", graphcontent)
    response = callKimi(prompt)
    return response

def answerbyGraph2(query):
    response = callKimi(query)
    return response

while True:
    query = input("请输入: ")#.split()
    # flag = int(flag)
    chat_history =[]
    g_assistant_response = None
    status = None
    status_table = []
    txt = None
    print("等待模型的回答ing...")
    emotp, response = emocheck(query,chat_history)
    if emotp == 0 or emotp == 1:
        print(f"模型的回答1：{response}")

    print("机器人判定",emotp)
    # if emotp == 1 or emotp == 2: #咨询或 情绪
    #     # lst = getKeywords(query,chat_history)
    #     # txt = queryGraph(lst)
    #     ret = answerbyGraph(query,'',chat_history)
    #     print(f"模型的回答2：{ret}")
    #     continue

    if emotp == 2 or emotp == 3:  # 办理类
        lst = getKeywords(query, chat_history)
        print(f"提取出的关键词：{lst}")
        txt = queryGraph(lst)
        print(f"根据关键词获取到的图谱内容：{txt}")
        if txt == "" or txt is None:
            print("问题与谱图知识无关，请转人工！")
        else:
            checkTag = checkGraph(query, txt)
            print(f"checkTag:{checkTag}")
            if checkTag == "1":
                ret = answerbyGraph(query, txt, chat_history)
                print(f"模型的回答3：{ret}")
            else:
                print("问题与谱图知识无关，请转人工！")
        continue

    if emotp == 1:  # 回复
        ret = answerbyGraph(query, '', chat_history)
        print(f"模型的回答3：{ret}")
        continue
        
        
    print(f"模型的回答4：{response}")
    




            