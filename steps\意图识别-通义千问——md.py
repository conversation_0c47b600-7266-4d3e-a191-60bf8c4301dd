import base64
import json
import os

import requests

question='内资有限公司需要变更法定代表人，需要什么材料？'
prompt = f"""请你严格按照指令进行判断，禁止你联网搜索资料、禁止你主观推断。

在我上传的JSON格式的知识图谱文件中，根节点“政务服务意图识别”直接连接的节点代表了不同的知识领域，这些节点的description属性描述了对应的知识领域定义。

请按照要求回复内容：
1、请判断这个问题属于哪个知识领域。如果找不到对应的知识领域节点，请直接返回“无法判断您的知识领域”，如果能找到就先输出：知识领域:知识领域节点名称。
2、然后你查询和这个知识领域节点直接相连的所有节点（不能遗漏），这些节点分别代表一个信息要素识别任务，这些节点为信息要素识别节点。信息要素识别节点的description属性内容是询问信息要素的问题。请你循环信息要素识别节点，从问题中读取当前信息要素识别节点和知识领域节点的关系的描述信息，按照描述信息规则对我输入的问题文字从语义理解而非简单的关键字包含角度判断是否需要识别（注意判断标准是按规则要求是否需要识别而非实际能否识别出来），如果不需要识别，直接输出：对应节点名称:无需识别”。
3、然后跳出本次循环；如果判断结果是需要识别，就先提取信息要素识别节点description属性中的选项清单，并尝试从我输入的问题文字中提取对应的要素，严谨地尝试能否对应到其中一个选项，如果能对应到，输出对应到的那个选项；否则输出：需要询问”。
4、以JSON格式返回结果，输出示例：{{"知识领域":"xxxx","确认主体类型":"xxxx","确认注销类型":"xxxx","确认注册地点":"xxxx"}}
5、请直接给我回复，无需分析过程。

问题是：{question}
"""

yxprompt=f"""请你严格按照指令进行判断，禁止你联网搜索资料、禁止你主观推断。

请先定义一个字符串res。

在我上传的JSON格式的知识图谱文件中，根节点“政务服务意图识别”直接连接的节点代表了不同的知识领域，这些节点的description属性描述了对应的知识领域定义。稍后我给你一个问题，请判断这个问题属于哪个知识领域。如果找不到对应的知识领域节点，res=“无法判断您的知识领域”，如果能找到，res=“知识领域：”+知识领域节点名称+换行。然后你查询和这个知识领域节点直接相连的所有节点（不能遗漏），这些节点分别代表一个信息要素识别任务，你称这些节点为信息要素识别节点。信息要素识别节点的description属性内容是询问信息要素的问题。请你循环信息要素识别节点，res+=节点名称+“：”，然后读取当前信息要素识别节点和知识领域节点的关系的描述信息，按照描述信息包含规则对我输入的问题文字从语义理解而非简单的关键字包含角度判断是否需要识别（注意判断标准是按规则要求是否需要识别而非实际能否识别出来），如果不需要识别，res+=“（无需识别）”，然后跳出本次循环；如果判断结果是需要识别，就先提取信息要素识别节点description属性中的选项清单，并尝试从我输入的问题文字中提取对应的要素，严谨地尝试能否对应到其中一个选项，如果能对应到，res+=你对应到的那个选项；否则res+=“（需要询问）”。

所有信息要素识别节点循环完成后，请用json格式输出res的内容，请不要把“res”包括进去，只需要转化res的值文本。
请直接给我回复，不要输出分析过程。

问题是：{question}"""

prompt2=f"""请你严格按照指令进行判断，禁止你联网搜索资料、禁止你主观推断。
在我上传的markdown格式的文件中，定义了"政务服务意图识别"下的不同的知识领域，每个知识领域给出了相应的定义。
每个知识领域下含有信息要素，信息要素含有识别条件和描述。识别条件表示在不同的问题下，该要素是否需要识别。描述表示询问该信息要素的问题。

请按照要求回复内容：
1、请判断问题属于哪个知识领域。如果找不到对应的知识领域，请直接返回“无法判断您的知识领域”，如果能找到就先输出：知识领域:知识领域名称。
2、然后你查询这个知识领域下的所有信息要素，根据识别条件判断是否需要识别这个信息要素，如果不需要识别，直接输出：对应节点名称:无需识别。如果需要识别，就根据描述，尝试从我输入的问题文字中提取对应的要素，严谨地尝试能否对应到其中一个选项，如果能对应到，输出对应到的那个选项；否则输出：需要询问”。
3、以JSON格式返回结果，输出示例：{{"知识领域":"xxxx","确认主体类型":"xxxx","确认注销类型":"xxxx","确认注册地点":"xxxx"}}
4、请直接给我回复，无需分析过程。

问题是：{question}"""

url = "http://192.168.186.18:8080/llm/predict"
parent_dir = os.path.dirname(os.getcwd())
file_path = os.path.join(parent_dir, 'data', '图谱md.txt')
with open(file_path, "rb") as f:
    file_stream = f.read()
file_b64 = base64.b64encode(file_stream).decode("utf-8")

stream = False

messages = [
    {"role": "user", "content": prompt2},
]

data = {
    "messages": messages,
    "files": [file_b64],   # 文件可以是多个
    "stream": stream
}


response = requests.request("POST", url, data=json.dumps(data))

print(json.loads(response.text)['result'])


file_path = os.path.join(parent_dir, 'data', '政务服务意图图谱可判断是否需要询问.txt')
with open(file_path, "rb") as f:
    file_stream = f.read()
file_b64 = base64.b64encode(file_stream).decode("utf-8")

stream = False

messages = [
    {"role": "user", "content": yxprompt},
]

data = {
    "messages": messages,
    "files": [file_b64],   # 文件可以是多个
    "stream": stream
}


response = requests.request("POST", url, data=json.dumps(data))

print(json.loads(response.text)['result'])


#
#
# fileContent = json.loads(file_stream)
# content = json.loads(response.text)['result']
# parsed_result = content
# domain = '无法判断您的知识领域'
# elements = {}
# missingElements = []
# content = content[content.index("\"") + 1:content.rindex("\"")]
# lines = content.split("\\n")
# if lines[0] != "无法判断您的知识领域":
#     for line in lines:
#         key, value = line.split("：", maxsplit=1)
#         if key == '知识领域':
#             domain = value
#             elements[key] = domain
#         else:
#             for entity in fileContent["entities"]:
#                 # 这里也不考虑同名的情况了
#                 if entity["name"] == key:
#                     if value == '（需要询问）':
#                         missingElements.append({"name":key,"desc":entity['description']})
#                     elif value != '（无需识别）':
#                         elements[key] = value
#                     break
# print(domain)
# print(json.dumps(elements, ensure_ascii=False))
# print(json.dumps(missingElements, ensure_ascii=False))
