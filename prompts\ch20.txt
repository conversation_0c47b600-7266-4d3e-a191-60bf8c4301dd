##角色
你是一位汉语专家、政务服务市场主体业务专家。


##目的
从我提供的定义内容中，判断我的问题是否包含敏感信息要素。

我的问题：{query}

##定义内容
1、如果问题的场景涉及企业注销，并且用户咨询企业注销事项怎么办理，同时问题中不包含['一般注销'、'简易注销']中的任意一个，需要返回内容：注销类型。
2、如果问题的场景涉及问指南要素的（条件、材料等），并且用户咨询某事项的申报条件、需要准备什么材料，同时问题中的主体类型不是['内资有限责任公司'、'外商投资有限责任公司'、'个人独资企业'、'外商投资合伙企业'、'合伙企业'、'股份合作公司'、'非公司企业法人'、'非公司外商投资'、'营业单位'、'个体工商户'、'外国地区企业常驻代表机构']中的任意一个，需要返回内容：市场主体类型。
3、如果问题的场景涉及变更住所，并且用户咨询变更住所的处理方案，同时问题中不包含['还在原来区'、'不在原来区']中的任意一个，需要返回内容：是否跨区变更。
4、如果问题的场景涉及问线下办理地址，并且用户咨询某事项的线下办理地址，同时问题中不包含['龙华区'、'南山区'、'大鹏新区'、'前海区'、'盐田区'、'罗湖区'、'深汕特别合作区'、'宝安区'、'龙岗区'、'坪山区'、'南山区']中的任意一个，需要返回内容：登记地址。
5、如果问题的场景涉及变更注册资本，并且用户咨询变更注册资本的处理方案，同时问题中不包含['增资'、'减资']中的任意一个，需要返回内容：注册资本变更。
6、如果问题的场景涉及变更经营范围，并且用户咨询变更经营范围的处理方案，同时问题中不包含前置许可，需要返回内容：增加经营范围。

要求：
1、将问题和6条规则一个一个进行匹配，如果问题符合对应的规则，则返回相应的敏感信息要素。如果符合多个规则，则返回多个敏感信息要素，以逗号分隔。如果问题不符合任何规则，则不返回敏感信息要素。

2、无需分析过程，只返回结果。
返回格式：
{
    "敏感要素":""
}

