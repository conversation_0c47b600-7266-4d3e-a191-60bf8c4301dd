{"name": "klsmcp", "version": "0.1.0", "description": "当问题是公积金领域的相关问题时使用工具进行回复", "private": true, "type": "module", "bin": {"klsmcp": "./build/index.js"}, "files": ["build"], "scripts": {"build": "tsc && node -e \"require('fs').chmodSync('build/index.js', '755')\"", "prepare": "npm run build", "watch": "tsc --watch", "inspector": "npx @modelcontextprotocol/inspector build/index.js"}, "dependencies": {"@modelcontextprotocol/sdk": "0.6.0"}, "devDependencies": {"@types/node": "^20.11.24", "typescript": "^5.3.3"}}