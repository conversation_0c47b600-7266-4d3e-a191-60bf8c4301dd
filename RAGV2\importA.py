#
import openpyxl

import requests
import json
import sys
import os

from uuid import uuid4
from openai import OpenAI

root_path = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
sys.path.append(root_path)
from steps.tools import get_prompt,qwen7b,qwenMax,call<PERSON>imi

from combine.ch5 import callKimi
from tqdm import tqdm
import datetime

import requests
from requests.models import HTTPBasicAuth



def callES(question,index_name,select_field,fields,num):
    inteligentsearch_url = "http://192.168.186.65/inteligentsearch/rest"
    params = {
        "isBusiness": 1,
        "cnum": "nzyxzrgs27",
        "esdsid": "8",
        "pn": 1,
        "rn": 20,
    }

    headers = {
        'Content-Type': 'application/json',
        "Authorization": "Bearer " + "token",
        "User-Agent": "python"
    }
    response = requests.post(url=inteligentsearch_url + "/esinteligentsearch/getFullTextDataNew",
                                data=json.dumps(params), headers=headers)
    # print(response.text)
    re = json.loads(response.text)
    results = eval(json.dumps(json.loads(re["content"])["result"]["records"]))
    return results,json.loads(re["content"])["result"]["totalcount"]


def createIndex(index_name):
    init_data = json.load(open("./RAGV2/schema.json",encoding="utf-8"))
    inteligentsearch_url = "http://192.168.186.65/inteligentsearch/rest"

    init_data["esdsid"] = 8
    init_data["category"]["categoryName"] = index_name
    init_data["category"]["categoryDescribe"] = index_name
    init_data["category"]["categoryNum"] = index_name

    init_data['category'] = json.dumps(init_data['category'])
        # {**(json.loads(init_data['category']) if isinstance(init_data['category'], str) else init_data['category']),
        #     'categoryNum': index_name}, ensure_ascii=False)
   
    build_url = inteligentsearch_url + "/esinteligentsearchmanageinit/addCategory"
    headers = {
        'Content-Type': 'application/json',
        "Authorization": "Bearer " + "token",
        "User-Agent": "python"
    }

    response = requests.post(url=build_url, json=init_data, headers=headers)
    print(response.text)

def embding(q_list):
    embed_url = "http://192.168.186.18:9081/linezhengshi_embedding/embed"
    data_embed = {"text": q_list}
    headers = {
        'Content-Type': 'application/json',
        "User-Agent": "python",
        "Authorization": "Bearer 4b1f2daed98265cc43aa7ebccfa89f0e",
    }
    response = requests.post(url=embed_url, data=json.dumps(data_embed), headers=headers)
    results = response.text
    return json.loads(results)["result"]



def insertData(ptype, index_name,purpose,subkey,content):
    es_url = "https://192.168.186.63:9200"
    id = str(uuid4())

    time_now = datetime.datetime.now().strftime('%Y-%m-%d')
    situation =""

    data = {
            "kworg_keyword":subkey,
            "kw_text":subkey,
            "kwvec_vector":embding([subkey])[0],

            "content_text":content,
            "contentvec_vector":embding([content])[0],   

            "ptype_keyword":ptype,
            "ptype_text":ptype,
            "ptypevec_vector":embding([ptype])[0],

            "purpose_keyword":purpose,
            "purpose_text":purpose,
            "purposevec_vector":embding([purpose])[0],

            "situation_text":situation,
            "situationvec_vector":embding([situation])[0],

            "id_keyword":id, "infodate_date": time_now, "syscategory_keyword": index_name
            }
    data = json.dumps(data)


    # result = self.data_inseart(self.es_url, index_name, data, id, self.username, self.password)
    headers = {'Content-Type': 'application/json'}
    insert_url = es_url + "/" + index_name + "/_doc" + "/"
    response = requests.put(insert_url + id, headers=headers, auth=HTTPBasicAuth("admin", "admin"),
                            verify=False, data=data.encode('utf-8'))
    try:
        re = json.loads(response.text)
        if "error" in re:
            error = re["error"]["type"]
            print(re)
            print(f"[*] insert应用中，导入数据失败，请检查传参，错误类型{error}")
        else:
            # pass
            result = re["result"]
            print(f"[*] 导入数据成功，主键：{id}，状态：{result}")
    except Exception as e:
        re = e
        print(f"[*] insert应用中， 导入数据出错，请检查es地址是否访问通，错误类型{e}，es接口返回{response.text}")
        


if __name__ == '__main__':
    index_name= "nzyxzrgsv2_1"
    createIndex(index_name)

    pg = 0
    total = 0
    while True:
        ret,cnt = callES("","nzyxzrgs27","path","path;content",pg)
        if len(ret) ==0:
            break
        if total > cnt:
            break
        total += len(ret)
        pg = pg + 1

        # print(ret)
        for item in ret:
            sbitem = item["path"].split("-")
            print(item["path"])
            ptype = sbitem[0]
            purpose = sbitem[1]
            subkey = sbitem[2] + "-" + sbitem[3]
            content = item["content"]
            result = insertData(ptype, index_name,purpose,subkey,content)
