from flask import Flask, request, jsonify
from ESApiClient import ESApiClient

app = Flask(__name__)

@app.route('/entitysearch', methods=['POST'])
def entitysearch():
    # 读取参数
    params = request.get_json(force=True)
    conditionlist = params.get("conditionlist", [])
    tablename = params.get("tablename", "")
    es_client = ESApiClient()
    new_conditionlist = []
    for cond in conditionlist:
        new_cond = cond.copy()
        fieldname = cond.get("fieldname")
        condition = [
            {
                "conditionList": [
                    {
                        "fieldname": fieldname
                    }
                ],
                "highlights": "false"
            },
            {
                "conditionList": [
                    {
                        "tablename": tablename
                    }
                ],
                "highlights": "false"
            }
        ]
        entity = cond.get("fieldvalue", "")
        record = {
            "question": entity,
            "index_name": "chatbidata0522002",
            "fields": "content",
            "selectfields": "content",
            "orgfield": "",
            "num": 1,
            "isfulltext": True,
            "accuracy": 70,
            "condition":condition
        }
        response = es_client.call_es_api(record)
        records = response.get("content", {}).get("result", {}).get("records", [])
        entityvalue = ""
        if records:
            entityvalue = records[0].get("content", "")
        if not entityvalue:
            entityvalue = cond.get("fieldvalue", "")
        new_cond["fieldvalue"] = entityvalue
        new_conditionlist.append(new_cond)

    return jsonify({"conditionlist": new_conditionlist})


@app.route('/qa_by_tableguid', methods=['POST'])
def qa_by_tableguid():
    try:
        params = request.get_json(force=True)
        tableguid = params.get("tableguid", "")
        if not tableguid:
            return jsonify({"error": "缺少 tableguid 参数"}), 400

        es_client = ESApiClient()
        response = es_client.query_qa_db(tableguid)
        return jsonify({"qa": response})
    except Exception as e:
        return jsonify({"error": str(e)}), 500


if __name__ == '__main__':
    app.run(host='127.0.0.1', port=5001)
