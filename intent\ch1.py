# 将用户的问题，拆分为寒喧、情绪、业务问题

# TODO 调一调提示词

import requests
import json
from steps.tools import get_prompt,qwen7b,qwenMax

# question = "内资企业怎么注销"
# question = "你好"
# question = "你们办理效率太低了"
# question = "你们办理效率太低了，内资企业怎么注销"
question = "我想企业注销"
messages = [
    {
        "role": "system",
        "content":f"""你是政府服务工作人员，对方是办事人，你给对方提供政务服务办事服务。
请你根据办事人最新的回复，结合对话上下文，判断目前处于哪种情形中。
我会给你3种情形，请你从中选择。
1.办事人回答你的询问
2.办事人表示听不懂你的询问或者表示不知道答案
3.办事人问了问题

如果是情形1，情形名称为"答案"，内容是办事人的回答。注意，你的问题会带有选项，你需要提炼办事人回答中匹配的选项，而不是办事人原始回答。
如果是情形2，情形名称为"听不懂"，内容返回空即可。
如果是情形3，情形名称为"用户提问"，请把他的问题基于上下文转写成一个或多个语义完整的独立问题作为内容。
针对情形3，用户提问可能会包含多个问题，请把每一个问题都基于上下文补充完整。

回复要求：
1、一个回复中可能包含多种清形，情形名称必须是上面的某一个情形，请同时进行回复。
2、请你以JSON数组形式回复，例如：[{{"condition":"答案","content":"内容"}}]。
3、针对情形3，如果分离出多个问题，content内容以json数组格式返回，例如：[{{"condition":"用户提问","content":["如何确定注销登记类型是一般注销登记还是简易注销登记？","一般注销登记和简易注销登记分别需要提交什么材料？"]}}]。

请直接给我回复，无需分析过程。"""
    }
    ,{
        "role":"user",
        "content":question
    },{
        "role":"assistant",
        "content":"请选择您的市场主体类型。你可从在以下选项中选择：内资有限责任公司、外资有限责任公司、内资股份有限公司、外资股份有限公司、内资合伙企业、外资合伙企业、个人独资企业、个体工商户。"
    },{
        "role":"user",
        "content":"内资股份"
    }
    ,{
        "role":"assistant",
        "content":"请说明你的注销登记类型。注销登记类型包括一般注销登记和简易注销登记"
    },{
        "role":"user",
        "content":"一般注销吧，这俩有啥区别嘛？提交材料有啥不一样？"
    }
]



response = qwenMax(messages)

print(response)

#总控模块，在拆出业务问题时就拆出多个问题