POST http://**************:8188/EpointFrame/rest/streammsgagent/stream?newid=8ccdd343e01542fe937b9792892da568
Authorization: Basic admin admin
Content-Type: application/json
Content-Length: 2
Connection: Keep-Alive
User-Agent: Apache-HttpClient/4.5.14 (Java/17.0.9)
Accept-Encoding: br,deflate,gzip,x-gzip

{}

<> 2025-09-02T171626.200.txt

###

POST http://**************:5016/rag/retrieve
Authorization: Basic admin admin
Content-Type: application/json
Content-Length: 2044
Connection: Keep-Alive
User-Agent: Apache-HttpClient/4.5.14 (Java/17.0.9)
Accept-Encoding: br,deflate,gzip,x-gzip

{
    "select_condition": {
        "attachguid": [
            null
        ]
    },
    "question": "知识库检索任务：针对“关于加快推进数字政府建设的工作报告”的公文写作，检索以下素材：\n\n1. **立场明确**：\n   - 政策文件及上级指示：《“十四五”数字经济发展规划》《国务院关于加强数字政府建设的指导意见》等相关政策文件。\n   - 党中央的相关会议决议和领导讲话，与数字中国、智慧社会建设相关的论述。\n\n2. **言之有物**：\n   - 我市在数字政府建设方面的具体成效和问题，特别是政务数据共享、网上办事、平台建设、营商环境等方面的详细数据。\n   - 具体工作目标，如政务数据共享交换平台建设、网上办事事项覆盖率、智能化监管等目标的具体数据和时间表。\n   - 相关案例研究，如其他城市或单位在数字政府建设中的成功案例，尤其是涉及数据共享、线上服务、平台建设和营商环境优化的案例。\n\n3. **形式规范**：\n   - 党政机关公文格式规范《党政机关公文格式》GB/T 9704-2012的具体要求。\n   - 标题和章节命名规则，确保各级标题表述简洁明了，符合公文标题的惯用语和格式要求。\n\n4. **逻辑自洽**：\n   - 数字政府建设的系统性逻辑架构，确保从问题分析到解决方案的逻辑自洽，论证过程严谨。\n   - 工作措施和目标之间的因果关系，确保每项措施和目标的解释与整体逻辑一致。\n\n5. **受众适配**：\n   - 针对不同受众的语言风格规范文档，如面向上级机关的汇报性语言、面向下级的指导性语言等。\n   - 公文受众分析，针对不同受众调整内容侧重点，确保信息传递准确高效。",
    "pageSize": "10",
    "mixretrieve_ratio": "1.0",
    "is_accsearch": "false",
    "index_name": [
        "88d022e5-f60a-4565-86e3-a64b250a7193",
        "f1a121f8-262a-46b8-b3f2-9f7e0095a0ed"
    ]
}

<> 2025-09-02T154707.200.html

###

POST http://**************:5016/rag/retrieve
Authorization: Basic admin admin
Content-Type: application/json
Content-Length: 2044
Connection: Keep-Alive
User-Agent: Apache-HttpClient/4.5.14 (Java/17.0.9)
Accept-Encoding: br,deflate,gzip,x-gzip

{
    "select_condition": {
        "attachguid": [
            null
        ]
    },
    "question": "知识库检索任务：针对“关于加快推进数字政府建设的工作报告”的公文写作，检索以下素材：\n\n1. **立场明确**：\n   - 政策文件及上级指示：《“十四五”数字经济发展规划》《国务院关于加强数字政府建设的指导意见》等相关政策文件。\n   - 党中央的相关会议决议和领导讲话，与数字中国、智慧社会建设相关的论述。\n\n2. **言之有物**：\n   - 我市在数字政府建设方面的具体成效和问题，特别是政务数据共享、网上办事、平台建设、营商环境等方面的详细数据。\n   - 具体工作目标，如政务数据共享交换平台建设、网上办事事项覆盖率、智能化监管等目标的具体数据和时间表。\n   - 相关案例研究，如其他城市或单位在数字政府建设中的成功案例，尤其是涉及数据共享、线上服务、平台建设和营商环境优化的案例。\n\n3. **形式规范**：\n   - 党政机关公文格式规范《党政机关公文格式》GB/T 9704-2012的具体要求。\n   - 标题和章节命名规则，确保各级标题表述简洁明了，符合公文标题的惯用语和格式要求。\n\n4. **逻辑自洽**：\n   - 数字政府建设的系统性逻辑架构，确保从问题分析到解决方案的逻辑自洽，论证过程严谨。\n   - 工作措施和目标之间的因果关系，确保每项措施和目标的解释与整体逻辑一致。\n\n5. **受众适配**：\n   - 针对不同受众的语言风格规范文档，如面向上级机关的汇报性语言、面向下级的指导性语言等。\n   - 公文受众分析，针对不同受众调整内容侧重点，确保信息传递准确高效。",
    "pageSize": "10",
    "mixretrieve_ratio": "1.0",
    "is_accsearch": "false",
    "index_name": [
        "88d022e5-f60a-4565-86e3-a64b250a7193",
        "f1a121f8-262a-46b8-b3f2-9f7e0095a0ed"
    ]
}

<> 2025-08-29T094234.200.html

###

POST http://**************:5016/rag/retrieve
Authorization: Basic admin admin
Content-Type: application/json
Content-Length: 2044
Connection: Keep-Alive
User-Agent: Apache-HttpClient/4.5.14 (Java/17.0.9)
Accept-Encoding: br,deflate,gzip,x-gzip

{
    "select_condition": {
        "attachguid": [
            null
        ]
    },
    "question": "知识库检索任务：针对“关于加快推进数字政府建设的工作报告”的公文写作，检索以下素材：\n\n1. **立场明确**：\n   - 政策文件及上级指示：《“十四五”数字经济发展规划》《国务院关于加强数字政府建设的指导意见》等相关政策文件。\n   - 党中央的相关会议决议和领导讲话，与数字中国、智慧社会建设相关的论述。\n\n2. **言之有物**：\n   - 我市在数字政府建设方面的具体成效和问题，特别是政务数据共享、网上办事、平台建设、营商环境等方面的详细数据。\n   - 具体工作目标，如政务数据共享交换平台建设、网上办事事项覆盖率、智能化监管等目标的具体数据和时间表。\n   - 相关案例研究，如其他城市或单位在数字政府建设中的成功案例，尤其是涉及数据共享、线上服务、平台建设和营商环境优化的案例。\n\n3. **形式规范**：\n   - 党政机关公文格式规范《党政机关公文格式》GB/T 9704-2012的具体要求。\n   - 标题和章节命名规则，确保各级标题表述简洁明了，符合公文标题的惯用语和格式要求。\n\n4. **逻辑自洽**：\n   - 数字政府建设的系统性逻辑架构，确保从问题分析到解决方案的逻辑自洽，论证过程严谨。\n   - 工作措施和目标之间的因果关系，确保每项措施和目标的解释与整体逻辑一致。\n\n5. **受众适配**：\n   - 针对不同受众的语言风格规范文档，如面向上级机关的汇报性语言、面向下级的指导性语言等。\n   - 公文受众分析，针对不同受众调整内容侧重点，确保信息传递准确高效。",
    "pageSize": "10",
    "mixretrieve_ratio": "1.0",
    "is_accsearch": "false",
    "index_name": [
        "88d022e5-f60a-4565-86e3-a64b250a7193",
        "f1a121f8-262a-46b8-b3f2-9f7e0095a0ed"
    ]
}

<> 2025-08-29T094108.200.html

###

POST http://*************:5015/wechat/talkv2
Authorization: Basic admin admin
Content-Type: application/json
Content-Length: 292
Connection: Keep-Alive
User-Agent: Apache-HttpClient/4.5.14 (Java/17.0.9)
Accept-Encoding: br,deflate,gzip,x-gzip

{
  "sender": "839214798327689723894723943",
  "message": "福建某公司提供虚假材料谋求中标案的处罚结果",
  "appguid": "da097a89-f080-4871-b085-48fd1c3c4189",
  "debug": "false",
  "stream": "true",
  "streamagent":"true",
  "extinfo": {
    "istest":"hahahahhhahaha"
  }
}

<> 2025-08-19T091918.200.html

###

POST http://*************:5015/wechat/talkv2
Authorization: Basic admin admin
Content-Type: application/json
Content-Length: 292
Connection: Keep-Alive
User-Agent: Apache-HttpClient/4.5.14 (Java/17.0.9)
Accept-Encoding: br,deflate,gzip,x-gzip

{
  "sender": "839214798327689723894723943",
  "message": "福建某公司提供虚假材料谋求中标案的处罚结果",
  "appguid": "da097a89-f080-4871-b085-48fd1c3c4189",
  "debug": "false",
  "stream": "true",
  "streamagent":"true",
  "extinfo": {
    "istest":"hahahahhhahaha"
  }
}

<> 2025-08-19T091651.200.html

###

POST http://*************:5015/wechat/talkv2
Authorization: Basic admin admin
Content-Type: application/json
Content-Length: 292
Connection: Keep-Alive
User-Agent: Apache-HttpClient/4.5.14 (Java/17.0.9)
Accept-Encoding: br,deflate,gzip,x-gzip

{
  "sender": "839214798327689723894723943",
  "message": "福建某公司提供虚假材料谋求中标案的处罚结果",
  "appguid": "da097a89-f080-4871-b085-48fd1c3c4189",
  "debug": "false",
  "stream": "true",
  "streamagent":"true",
  "extinfo": {
    "istest":"hahahahhhahaha"
  }
}

<> 2025-08-19T091529.200.html

###

POST http://*************:5015/wechat/talkv2
Authorization: Basic admin admin
Content-Type: application/json
Content-Length: 296
Connection: Keep-Alive
User-Agent: Apache-HttpClient/4.5.14 (Java/17.0.9)
Accept-Encoding: br,deflate,gzip,x-gzip

{
  "sender": "1287389127893721897398127873219",
  "message": "福建某公司提供虚假材料谋求中标案的处罚结果",
  "appguid": "da097a89-f080-4871-b085-48fd1c3c4189",
  "debug": "false",
  "stream": "true",
  "streamagent":"true",
  "extinfo": {
    "istest":"hahahahhhahaha"
  }
}

<> 2025-08-19T091518.200.html

###

POST http://*************:5015/wechat/talkv2
Authorization: Basic admin admin
Content-Type: application/json
Content-Length: 296
Connection: Keep-Alive
User-Agent: Apache-HttpClient/4.5.14 (Java/17.0.9)
Accept-Encoding: br,deflate,gzip,x-gzip

{
  "sender": "1287389127893721897398127873219",
  "message": "福建某公司提供虚假材料谋求中标案的处罚结果",
  "appguid": "da097a89-f080-4871-b085-48fd1c3c4189",
  "debug": "false",
  "stream": "true",
  "streamagent":"true",
  "extinfo": {
    "istest":"hahahahhhahaha"
  }
}

<> 2025-08-19T091409.200.html

###

POST http://*************:5015/wechat/talkv2
Authorization: Basic admin admin
Content-Type: application/json
Content-Length: 297
Connection: Keep-Alive
User-Agent: Apache-HttpClient/4.5.14 (Java/17.0.9)
Accept-Encoding: br,deflate,gzip,x-gzip

{
  "sender": "1287389127893721897398127873219",
  "message": "福建某公司提供虚假材料谋求中标案的处罚结果",
  "appguid": "da097a89-f080-4871-b085-48fd1c3c4189",
  "debug": "false",
  "stream": "false",
  "streamagent":"true",
  "extinfo": {
    "istest":"hahahahhhahaha"
  }
}

<> 2025-08-19T091359.200.html

###

POST http://*************:5015/wechat/talkv2
Authorization: Basic admin admin
Content-Type: application/json
Content-Length: 290
Connection: Keep-Alive
User-Agent: Apache-HttpClient/4.5.14 (Java/17.0.9)
Accept-Encoding: br,deflate,gzip,x-gzip

{
  "sender": "839823908492839423942343",
  "message": "福建某公司提供虚假材料谋求中标案的处罚结果",
  "appguid": "da097a89-f080-4871-b085-48fd1c3c4189",
  "debug": "false",
  "stream": "false",
  "streamagent":"true",
  "extinfo": {
    "istest":"hahahahhhahaha"
  }
}

<> 2025-08-19T091239.200.html

###

POST http://*************:5015/wechat/talkv2
Authorization: Basic admin admin
Content-Type: application/json
Content-Length: 290
Connection: Keep-Alive
User-Agent: Apache-HttpClient/4.5.14 (Java/17.0.9)
Accept-Encoding: br,deflate,gzip,x-gzip

{
  "sender": "839823908492839423942343",
  "message": "福建某公司提供虚假材料谋求中标案的处罚结果",
  "appguid": "da097a89-f080-4871-b085-48fd1c3c4189",
  "debug": "false",
  "stream": "false",
  "streamagent":"true",
  "extinfo": {
    "istest":"hahahahhhahaha"
  }
}

<> 2025-08-19T091149.200.html

###

POST http://*************:5015/wechat/talkv2
Authorization: Basic admin admin
Content-Type: application/json
Content-Length: 290
Connection: Keep-Alive
User-Agent: Apache-HttpClient/4.5.14 (Java/17.0.9)
Accept-Encoding: br,deflate,gzip,x-gzip

{
  "sender": "839823908492839423942343",
  "message": "福建某公司提供虚假材料谋求中标案的处罚结果",
  "appguid": "da097a89-f080-4871-b085-48fd1c3c4189",
  "debug": "false",
  "stream": "false",
  "streamagent":"true",
  "extinfo": {
    "istest":"hahahahhhahaha"
  }
}

<> 2025-08-19T091122.200.html

###

POST http://*************:5015/wechat/talkv2
Authorization: Basic admin admin
Content-Type: application/json
Content-Length: 290
Connection: Keep-Alive
User-Agent: Apache-HttpClient/4.5.14 (Java/17.0.9)
Accept-Encoding: br,deflate,gzip,x-gzip

{
  "sender": "839823908492839423942343",
  "message": "福建某公司提供虚假材料谋求中标案的处罚结果",
  "appguid": "da097a89-f080-4871-b085-48fd1c3c4189",
  "debug": "false",
  "stream": "false",
  "streamagent":"true",
  "extinfo": {
    "istest":"hahahahhhahaha"
  }
}

<> 2025-08-19T091038.200.html

###

POST http://*************:5015/wechat/talkv2
Authorization: Basic admin admin
Content-Type: application/json
Content-Length: 242
Connection: Keep-Alive
User-Agent: Apache-HttpClient/4.5.14 (Java/17.0.9)
Accept-Encoding: br,deflate,gzip,x-gzip

{
  "sender": "839823908492839423942343",
  "message": "身份证标准",
  "appguid": "da097a89-f080-4871-b085-48fd1c3c4189",
  "debug": "false",
  "stream": "false",
  "streamagent":"true",
  "extinfo": {
    "istest":"hahahahhhahaha"
  }
}

<> 2025-08-19T090741.200.html

###

POST http://*************:5015/wechat/talkv2
Authorization: Basic admin admin
Content-Type: application/json
Content-Length: 243
Connection: Keep-Alive
User-Agent: Apache-HttpClient/4.5.14 (Java/17.0.9)
Accept-Encoding: br,deflate,gzip,x-gzip

{
  "sender": "839823908492839423942343",
  "message": "身份证标准",
  "appguid": "da097a89-f080-4871-b085-48fd1c3c4189",
  "debug": "false",
  "stream": "false",
  "streamagent":"false",
  "extinfo": {
    "istest":"hahahahhhahaha"
  }
}

<> 2025-08-19T090638.200.html

###

POST http://*************:5015/wechat/talkv2
Authorization: Basic admin admin
Content-Type: application/json
Content-Length: 243
Connection: Keep-Alive
User-Agent: Apache-HttpClient/4.5.14 (Java/17.0.9)
Accept-Encoding: br,deflate,gzip,x-gzip

{
  "sender": "839823908492839423942343",
  "message": "身份证标准",
  "appguid": "8a2aeb69-d47f-47d8-9103-d0f9c0eec55c",
  "debug": "false",
  "stream": "false",
  "streamagent":"false",
  "extinfo": {
    "istest":"hahahahhhahaha"
  }
}

<> 2025-08-19T090354.200.html

###

POST http://*************:5015/wechat/talkv2
Authorization: Basic admin admin
Content-Type: application/json
Content-Length: 243
Connection: Keep-Alive
User-Agent: Apache-HttpClient/4.5.14 (Java/17.0.9)
Accept-Encoding: br,deflate,gzip,x-gzip

{
  "sender": "839823908492839423942343",
  "message": "身份证标准",
  "appguid": "8a2aeb69-d47f-47d8-9103-d0f9c0eec55c",
  "debug": "false",
  "stream": "false",
  "streamagent":"false",
  "extinfo": {
    "istest":"hahahahhhahaha"
  }
}

<> 2025-08-18T172443.200.html

###

POST http://*************:5015/wechat/talk
Authorization: Basic admin admin
Content-Type: application/json
Content-Length: 243
Connection: Keep-Alive
User-Agent: Apache-HttpClient/4.5.14 (Java/17.0.9)
Accept-Encoding: br,deflate,gzip,x-gzip

{
  "sender": "839823908492839423942343",
  "message": "身份证标准",
  "appguid": "8a2aeb69-d47f-47d8-9103-d0f9c0eec55c",
  "debug": "false",
  "stream": "false",
  "streamagent":"false",
  "extinfo": {
    "istest":"hahahahhhahaha"
  }
}

<> 2025-08-18T171829.200.html

###

POST http://*************:5015/wechat/talkv2
Authorization: Basic admin admin
Content-Type: application/json
Content-Length: 243
Connection: Keep-Alive
User-Agent: Apache-HttpClient/4.5.14 (Java/17.0.9)
Accept-Encoding: br,deflate,gzip,x-gzip

{
  "sender": "839823908492839423942343",
  "message": "身份证标准",
  "appguid": "8a2aeb69-d47f-47d8-9103-d0f9c0eec55c",
  "debug": "false",
  "stream": "false",
  "streamagent":"false",
  "extinfo": {
    "istest":"hahahahhhahaha"
  }
}

<> 2025-08-18T171813.200.html

###

POST http://*************:5015/wechat/talkv2
Authorization: Basic admin admin
Content-Type: application/json
Content-Length: 243
Connection: Keep-Alive
User-Agent: Apache-HttpClient/4.5.14 (Java/17.0.9)
Accept-Encoding: br,deflate,gzip,x-gzip

{
  "sender": "839823908492839423942343",
  "message": "身份证标准",
  "appguid": "8a2aeb69-d47f-47d8-9103-d0f9c0eec55c",
  "debug": "false",
  "stream": "false",
  "streamagent":"false",
  "extinfo": {
    "istest":"hahahahhhahaha"
  }
}

<> 2025-08-18T170639.200.html

###

POST http://*************:5015/wechat/talkv2
Authorization: Basic admin admin
Content-Type: application/json
Content-Length: 243
Connection: Keep-Alive
User-Agent: Apache-HttpClient/4.5.14 (Java/17.0.9)
Accept-Encoding: br,deflate,gzip,x-gzip

{
  "sender": "839823908492839423942343",
  "message": "身份证标准",
  "appguid": "8a2aeb69-d47f-47d8-9103-d0f9c0eec55c",
  "debug": "false",
  "stream": "false",
  "streamagent":"false",
  "extinfo": {
    "istest":"hahahahhhahaha"
  }
}

<> 2025-08-18T145914.200.html

###

POST http://*************:5015/wechat/talkv2
Authorization: Basic admin admin
Content-Type: application/json
Content-Length: 228
Connection: Keep-Alive
User-Agent: Apache-HttpClient/4.5.14 (Java/17.0.9)
Accept-Encoding: br,deflate,gzip,x-gzip

{
  "sender": "123121232",
  "message": "身份证标准",
  "appguid": "8a2aeb69-d47f-47d8-9103-d0f9c0eec55c",
  "debug": "false",
  "stream": "false",
  "streamagent":"false",
  "extinfo": {
    "istest":"hahahahhhahaha"
  }
}

<> 2025-08-18T145900.200.html

###

POST http://*************:5005/wechat/talkv2
Authorization: Basic admin admin
Content-Type: application/json
Content-Length: 228
Connection: Keep-Alive
User-Agent: Apache-HttpClient/4.5.14 (Java/17.0.9)
Accept-Encoding: br,deflate,gzip,x-gzip

{
  "sender": "123121232",
  "message": "身份证标准",
  "appguid": "8a2aeb69-d47f-47d8-9103-d0f9c0eec55c",
  "debug": "false",
  "stream": "false",
  "streamagent":"false",
  "extinfo": {
    "istest":"hahahahhhahaha"
  }
}

<> 2025-08-18T144739.200.html

###

POST http://*************:5005/wechat/talkv2
Authorization: Basic admin admin
Content-Type: application/json
Content-Length: 227
Connection: Keep-Alive
User-Agent: Apache-HttpClient/4.5.14 (Java/17.0.9)
Accept-Encoding: br,deflate,gzip,x-gzip

{
  "sender": "123121232",
  "message": "身份证标准",
  "appguid": "8a2aeb69-d47f-47d8-9103-d0f9c0eec55c",
  "debug": "true",
  "stream": "false",
  "streamagent":"false",
  "extinfo": {
    "istest":"hahahahhhahaha"
  }
}

<> 2025-08-18T144719.200.html

###

POST http://*************:5005/wechat/talkv2
Authorization: Basic admin admin
Content-Type: application/json
Content-Length: 227
Connection: Keep-Alive
User-Agent: Apache-HttpClient/4.5.14 (Java/17.0.9)
Accept-Encoding: br,deflate,gzip,x-gzip

{
  "sender": "123121232",
  "message": "身份证标准",
  "appguid": "8a2aeb69-d47f-47d8-9103-d0f9c0eec55c",
  "debug": "true",
  "stream": "false",
  "streamagent":"false",
  "extinfo": {
    "istest":"hahahahhhahaha"
  }
}

<> 2025-08-18T144154.200.html

###

POST http://*************:5005/wechat/talkv2
Authorization: Basic admin admin
Content-Type: application/json
Content-Length: 227
Connection: Keep-Alive
User-Agent: Apache-HttpClient/4.5.14 (Java/17.0.9)
Accept-Encoding: br,deflate,gzip,x-gzip

{
  "sender": "123121232",
  "message": "身份证标准",
  "appguid": "8a2aeb69-d47f-47d8-9103-d0f9c0eec55c",
  "debug": "true",
  "stream": "false",
  "streamagent":"false",
  "extinfo": {
    "istest":"hahahahhhahaha"
  }
}

<> 2025-08-18T144122.200.html

###

POST http://*************:5005/wechat/talkv2
Authorization: Basic admin admin
Content-Type: application/json
Content-Length: 231
Connection: Keep-Alive
User-Agent: Apache-HttpClient/4.5.14 (Java/17.0.9)
Accept-Encoding: br,deflate,gzip,x-gzip

{
  "sender": "3937373783783",
  "message": "身份证标准",
  "appguid": "8a2aeb69-d47f-47d8-9103-d0f9c0eec55c",
  "debug": "true",
  "stream": "false",
  "streamagent":"false",
  "extinfo": {
    "istest":"hahahahhhahaha"
  }
}

<> 2025-08-18T144059.200.html

###

POST http://*************:5005/wechat/talkv2
Authorization: Basic admin admin
Content-Type: application/json
Content-Length: 249
Connection: Keep-Alive
User-Agent: Apache-HttpClient/4.5.14 (Java/17.0.9)
Accept-Encoding: br,deflate,gzip,x-gzip

{
  "sender": "1238912738126789127638921738921",
  "message": "身份证标准",
  "appguid": "8a2aeb69-d47f-47d8-9103-d0f9c0eec55c",
  "debug": "true",
  "stream": "false",
  "streamagent":"false",
  "extinfo": {
    "istest":"hahahahhhahaha"
  }
}

<> 2025-08-18T144041.200.html

###

POST http://*************:5005/wechat/talkv2
Authorization: Basic admin admin
Content-Type: application/json
Content-Length: 249
Connection: Keep-Alive
User-Agent: Apache-HttpClient/4.5.14 (Java/17.0.9)
Accept-Encoding: br,deflate,gzip,x-gzip

{
  "sender": "1238912738126789127638921738921",
  "message": "身份证标准",
  "appguid": "8a2aeb69-d47f-47d8-9103-d0f9c0eec55c",
  "debug": "true",
  "stream": "false",
  "streamagent":"false",
  "extinfo": {
    "istest":"hahahahhhahaha"
  }
}

<> 2025-08-18T144020.200.html

###

POST http://*************:5005/wechat/talkv2
Authorization: Basic admin admin
Content-Type: application/json
Content-Length: 249
Connection: Keep-Alive
User-Agent: Apache-HttpClient/4.5.14 (Java/17.0.9)
Accept-Encoding: br,deflate,gzip,x-gzip

{
  "sender": "1238912738126789127638921738921",
  "message": "身份证标准",
  "appguid": "8a2aeb69-d47f-47d8-9103-d0f9c0eec55c",
  "debug": "true",
  "stream": "false",
  "streamagent":"false",
  "extinfo": {
    "istest":"hahahahhhahaha"
  }
}

<> 2025-08-18T144000.200.html

###

POST http://*************:5005/wechat/talkv2
Authorization: Basic admin admin
Content-Type: application/json
Content-Length: 249
Connection: Keep-Alive
User-Agent: Apache-HttpClient/4.5.14 (Java/17.0.9)
Accept-Encoding: br,deflate,gzip,x-gzip

{
  "sender": "1238912738126789127638921738921",
  "message": "身份证标准",
  "appguid": "8a2aeb69-d47f-47d8-9103-d0f9c0eec55c",
  "debug": "true",
  "stream": "false",
  "streamagent":"false",
  "extinfo": {
    "istest":"hahahahhhahaha"
  }
}

<> 2025-08-18T143938.200.html

###

POST http://*************:5005/wechat/talkv2
Authorization: Basic admin admin
Content-Type: application/json
Content-Length: 249
Connection: Keep-Alive
User-Agent: Apache-HttpClient/4.5.14 (Java/17.0.9)
Accept-Encoding: br,deflate,gzip,x-gzip

{
  "sender": "1238912738126789127638921738921",
  "message": "身份证标准",
  "appguid": "8a2aeb69-d47f-47d8-9103-d0f9c0eec55c",
  "debug": "true",
  "stream": "false",
  "streamagent":"false",
  "extinfo": {
    "istest":"hahahahhhahaha"
  }
}

<> 2025-08-18T143917.200.html

###

POST http://*************:5005/wechat/talkv2
Authorization: Basic admin admin
Content-Type: application/json
Content-Length: 249
Connection: Keep-Alive
User-Agent: Apache-HttpClient/4.5.14 (Java/17.0.9)
Accept-Encoding: br,deflate,gzip,x-gzip

{
  "sender": "1238912738126789127638921738921",
  "message": "身份证标准",
  "appguid": "8a2aeb69-d47f-47d8-9103-d0f9c0eec55c",
  "debug": "true",
  "stream": "false",
  "streamagent":"false",
  "extinfo": {
    "istest":"hahahahhhahaha"
  }
}

<> 2025-08-18T143715.200.html

###

POST http://*************:5005/wechat/talkv2
Authorization: Basic admin admin
Content-Type: application/json
Content-Length: 249
Connection: Keep-Alive
User-Agent: Apache-HttpClient/4.5.14 (Java/17.0.9)
Accept-Encoding: br,deflate,gzip,x-gzip

{
  "sender": "1238912738126789127638921738921",
  "message": "身份证标准",
  "appguid": "8a2aeb69-d47f-47d8-9103-d0f9c0eec55c",
  "debug": "true",
  "stream": "false",
  "streamagent":"false",
  "extinfo": {
    "istest":"hahahahhhahaha"
  }
}

<> 2025-08-18T143335.200.html

###

POST http://*************:5005/wechat/talkv2
Authorization: Basic admin admin
Content-Type: application/json
Content-Length: 250
Connection: Keep-Alive
User-Agent: Apache-HttpClient/4.5.14 (Java/17.0.9)
Accept-Encoding: br,deflate,gzip,x-gzip

{
  "sender": "1238912738126789127638921738921",
  "message": "身份证标准",
  "appguid": "8a2aeb69-d47f-47d8-9103-d0f9c0eec55c",
  "debug": "false",
  "stream": "false",
  "streamagent":"false",
  "extinfo": {
    "istest":"hahahahhhahaha"
  }
}

<> 2025-08-18T143240.200.html

###

POST http://*************:5005/wechat/talkv2
Authorization: Basic admin admin
Content-Type: application/json
Content-Length: 250
Connection: Keep-Alive
User-Agent: Apache-HttpClient/4.5.14 (Java/17.0.9)
Accept-Encoding: br,deflate,gzip,x-gzip

{
  "sender": "1238912738126789127638921738921",
  "message": "身份证标准",
  "appguid": "8a2aeb69-d47f-47d8-9103-d0f9c0eec55c",
  "debug": "false",
  "stream": "false",
  "streamagent":"false",
  "extinfo": {
    "istest":"hahahahhhahaha"
  }
}

<> 2025-08-18T143158.200.html

###

POST http://*************:5005/wechat/talkv2
Authorization: Basic admin admin
Content-Type: application/json
Content-Length: 250
Connection: Keep-Alive
User-Agent: Apache-HttpClient/4.5.14 (Java/17.0.9)
Accept-Encoding: br,deflate,gzip,x-gzip

{
  "sender": "1238912738126789127638921738921",
  "message": "身份证标准",
  "appguid": "8a2aeb69-d47f-47d8-9103-d0f9c0eec55c",
  "debug": "false",
  "stream": "false",
  "streamagent":"false",
  "extinfo": {
    "istest":"hahahahhhahaha"
  }
}

<> 2025-08-18T143117.200.html

###

POST http://*************:5005/wechat/talkv2
Authorization: Basic admin admin
Content-Type: application/json
Content-Length: 242
Connection: Keep-Alive
User-Agent: Apache-HttpClient/4.5.14 (Java/17.0.9)
Accept-Encoding: br,deflate,gzip,x-gzip

{
  "sender": "1iu2u2嚄21912739217372",
  "message": "身份证标准",
  "appguid": "8a2aeb69-d47f-47d8-9103-d0f9c0eec55c",
  "debug": "false",
  "stream": "false",
  "streamagent":"false",
  "extinfo": {
    "istest":"hahahahhhahaha"
  }
}

<> 2025-08-18T142906.200.html

###

POST http://*************:5005/wechat/talkv2
Authorization: Basic admin admin
Content-Type: application/json
Content-Length: 242
Connection: Keep-Alive
User-Agent: Apache-HttpClient/4.5.14 (Java/17.0.9)
Accept-Encoding: br,deflate,gzip,x-gzip

{
  "sender": "1iu2u2嚄21912739217372",
  "message": "身份证标准",
  "appguid": "8a2aeb69-d47f-47d8-9103-d0f9c0eec55c",
  "debug": "false",
  "stream": "false",
  "streamagent":"false",
  "extinfo": {
    "istest":"hahahahhhahaha"
  }
}

<> 2025-08-18T142812.200.html

###

POST http://*************:5005/wechat/talkv2
Authorization: Basic admin admin
Content-Type: application/json
Content-Length: 242
Connection: Keep-Alive
User-Agent: Apache-HttpClient/4.5.14 (Java/17.0.9)
Accept-Encoding: br,deflate,gzip,x-gzip

{
  "sender": "1iu2u2嚄21912739217372",
  "message": "身份证标准",
  "appguid": "8a2aeb69-d47f-47d8-9103-d0f9c0eec55c",
  "debug": "false",
  "stream": "false",
  "streamagent":"false",
  "extinfo": {
    "istest":"hahahahhhahaha"
  }
}

<> 2025-08-18T141936.200.html

###

POST http://*************:5005/wechat/talkv2
Authorization: Basic admin admin
Content-Type: application/json
Content-Length: 241
Connection: Keep-Alive
User-Agent: Apache-HttpClient/4.5.14 (Java/17.0.9)
Accept-Encoding: br,deflate,gzip,x-gzip

{
  "sender": "123dds1321312312312312",
  "message": "身份证标准",
  "appguid": "8a2aeb69-d47f-47d8-9103-d0f9c0eec55c",
  "debug": "false",
  "stream": "false",
  "streamagent":"false",
  "extinfo": {
    "istest":"hahahahhhahaha"
  }
}

<> 2025-08-18T141930.200.html

###

POST http://*************:5005/wechat/talkv2
Authorization: Basic admin admin
Content-Type: application/json
Content-Length: 241
Connection: Keep-Alive
User-Agent: Apache-HttpClient/4.5.14 (Java/17.0.9)
Accept-Encoding: br,deflate,gzip,x-gzip

{
  "sender": "123dds1321312312312312",
  "message": "身份证标准",
  "appguid": "8a2aeb69-d47f-47d8-9103-d0f9c0eec55c",
  "debug": "false",
  "stream": "false",
  "streamagent":"false",
  "extinfo": {
    "istest":"hahahahhhahaha"
  }
}

<> 2025-08-18T141655.200.html

###

POST http://*************:5005/wechat/talkv2
Authorization: Basic admin admin
Content-Type: application/json
Content-Length: 241
Connection: Keep-Alive
User-Agent: Apache-HttpClient/4.5.14 (Java/17.0.9)
Accept-Encoding: br,deflate,gzip,x-gzip

{
  "sender": "123dds1321312312312312",
  "message": "身份证标准",
  "appguid": "8a2aeb69-d47f-47d8-9103-d0f9c0eec55c",
  "debug": "false",
  "stream": "false",
  "streamagent":"false",
  "extinfo": {
    "istest":"hahahahhhahaha"
  }
}

<> 2025-08-18T141618.200.html

###

POST http://*************:5005/wechat/talkv2
Authorization: Basic admin admin
Content-Type: application/json
Content-Length: 241
Connection: Keep-Alive
User-Agent: Apache-HttpClient/4.5.14 (Java/17.0.9)
Accept-Encoding: br,deflate,gzip,x-gzip

{
  "sender": "123dds1321312312312312",
  "message": "身份证标准",
  "appguid": "8a2aeb69-d47f-47d8-9103-d0f9c0eec55c",
  "debug": "false",
  "stream": "false",
  "streamagent":"false",
  "extinfo": {
    "istest":"hahahahhhahaha"
  }
}

<> 2025-08-18T141423.200.html

###

POST http://*************:5005/wechat/talk
Authorization: Basic admin admin
Content-Type: application/json
Content-Length: 240
Connection: Keep-Alive
User-Agent: Apache-HttpClient/4.5.14 (Java/17.0.9)
Accept-Encoding: br,deflate,gzip,x-gzip

{
  "sender": "123dds1321312312312312",
  "message": "身份证标准",
  "appguid": "8a2aeb69-d47f-47d8-9103-d0f9c0eec55c",
  "debug": "true",
  "stream": "false",
  "streamagent":"false",
  "extinfo": {
    "istest":"hahahahhhahaha"
  }
}

<> 2025-08-18T140116.200.html

###

POST http://*************:5005/wechat/talkv2
Authorization: Basic admin admin
Content-Type: application/json
Content-Length: 190
Connection: Keep-Alive
User-Agent: Apache-HttpClient/4.5.14 (Java/17.0.9)
Accept-Encoding: br,deflate,gzip,x-gzip

{
  "sender": "123dds1321312312312312",
  "message": "身份证标准",
  "appguid": "8a2aeb69-d47f-47d8-9103-d0f9c0eec55c",
  "debug": "true",
  "stream": "false",
  "streamagent":"false"
}

<> 2025-08-18T140102.200.html

###

POST http://*************:5005/wechat/talkv2
Authorization: Basic admin admin
Content-Type: application/json
Content-Length: 240
Connection: Keep-Alive
User-Agent: Apache-HttpClient/4.5.14 (Java/17.0.9)
Accept-Encoding: br,deflate,gzip,x-gzip

{
  "sender": "123dds1321312312312312",
  "message": "身份证标准",
  "appguid": "8a2aeb69-d47f-47d8-9103-d0f9c0eec55c",
  "debug": "true",
  "stream": "false",
  "streamagent":"false",
  "extinfo": {
    "istest":"hahahahhhahaha"
  }
}

<> 2025-08-18T135922.200.html

###

POST http://*************:5005/wechat/talkv2
Authorization: Basic admin admin
Content-Type: application/json
Content-Length: 241
Connection: Keep-Alive
User-Agent: Apache-HttpClient/4.5.14 (Java/17.0.9)
Accept-Encoding: br,deflate,gzip,x-gzip

{
  "sender": "123dds1321312312312312",
  "message": "身份证标准",
  "appguid": "8a2aeb69-d47f-47d8-9103-d0f9c0eec55c",
  "debug": "false",
  "stream": "false",
  "streamagent":"false",
  "extinfo": {
    "istest":"hahahahhhahaha"
  }
}

<> 2025-08-18T135836.200.html

###

POST http://*************:5005/wechat/talkv2
Authorization: Basic admin admin
Content-Type: application/json
Content-Length: 242
Connection: Keep-Alive
User-Agent: Apache-HttpClient/4.5.14 (Java/17.0.9)
Accept-Encoding: br,deflate,gzip,x-gzip

{
  "sender": "12312312931289381298312",
  "message": "身份证标准",
  "appguid": "8a2aeb69-d47f-47d8-9103-d0f9c0eec55c",
  "debug": "false",
  "stream": "false",
  "streamagent":"false",
  "extinfo": {
    "istest":"hahahahhhahaha"
  }
}

<> 2025-08-18T135758.200.html

###

