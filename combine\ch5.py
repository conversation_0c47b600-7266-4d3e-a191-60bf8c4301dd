import json
import os
import re

import requests
from openai import OpenAI

client = OpenAI(
    api_key = "sk-NOOv7U6XgWdCwLmkqVbnC14YIJjvzR1ZQNWezNJEC9L29j3S", # 在这里将 MOONSHOT_API_KEY 替换为你从 Kimi 开放平台申请的 API Key
    base_url = "https://api.moonshot.cn/v1",
)
messages = [
	{"role": "system", "content": "请不要查询任何联网信息，也不需要调用大模型自己已有的知识储备，你现是为位政务服务中心的工作人员，负责处理用户的问题。禁止做出任何的假设和推测，要严格根据问题进行回答和反问政务服务中心代表着政府，做事必须有依有据，不能随意根据自己的经验来。"},
]

def callKimi(input: str) -> str:
    # 我们将用户最新的问题构造成一个 message（role=user），并添加到 messages 的尾部
    messages.append({
        "role": "user",
        "content": input,
    })

    # 携带 messages 与 Kimi 大模型对话
    completion = client.chat.completions.create(
        model="moonshot-v1-8k",
        messages=messages,
        temperature=0,
    )

    # 通过 API 我们获得了 Kimi 大模型给予我们的回复消息（role=assistant）
    assistant_message = completion.choices[0].message

    # 为了让 Kimi 大模型拥有完整的记忆，我们必须将 Kimi 大模型返回给我们的消息也添加到 messages 中
    messages.append(assistant_message)

    return assistant_message.content

def get_prompt(path,query,history):
    root_path = os.path.dirname(os.path.abspath(__file__))

    prompt = "".join( open(root_path + "/../template2/" + path, "r", encoding = "utf-8").readlines() )
    prompt = prompt.replace("{query}", query)
    prompt = prompt.replace("{sentence}", query)
    prompt = prompt.replace("{chat_history}", json.dumps(history, ensure_ascii=False))
    
    # 构建 JSON 文件路径
    base_name = path.replace("_template.txt","")
    json_file = os.path.join("template", base_name + ".json")
    if os.path.exists(json_file):
        with open(json_file, "r", encoding="utf-8") as f:
            json_data = json.load(f)  # 读取 JSON 内容
            json_str = json.dumps(json_data, ensure_ascii=False)  # 转为字符串
            lstname = "{" +base_name + "_list}"
            prompt = prompt.replace(lstname, json_str)  # 替换 {{json名}}
            
    return prompt

def emocheck(query,chat_history):
    response = callKimi(get_prompt("emocheck_template.txt",query,[]))
    if response != "" and response[0] =='2':
        return 2,""
    elif response != "" and response[0] =='3':
        return 3,""
    elif response != "" and response[0] =='0':
        return 0,response[2:]
    elif response != "" and response[0] =='1':
        return 1,response[2:]
    else:
        return "", response

def getKeywords(query,chat_history):
    for i in range(5):
        try:
            response = callKimi(get_prompt("getkeywords_template.txt",query,[]))
            json_data = json.loads(response)
            return json_data["关键词"]
        except:
            continue
    return response

def queryGraph(keywords):
    url = "http://127.0.0.1:5000/querygraph"

    payload = "{\"keywords\":\"" + keywords +"\",\"graphurl\":\"http://**************:40436/relation_nzyxzrgs/gremlin\",\"index_name\":\"nzyxzrgs\"}"
    headers = {
        'user-agent': "vscode-restclient",
        'content-type': "application/json"
        }

    response = requests.request("POST", url, data=payload, headers=headers)
    finialResult = response.json()["finalResult"]
    if response.json()["graphlist"] != '':
        resultSet = set()
        pattern = re.compile(r'\[(.*?)\]')
        #获取集合中的[]内的节点id
        for item in response.json()["graphlist"] :
            result = pattern.findall(item)
            if result:
                for codeId in result:
                    fibonacci(codeId,resultSet)

        if resultSet:

            for id in resultSet :
                #先查询当前节点的name
                name = findEntityName(id)
                name = getTotalName(id,name)
                resultStr = "[" + name + "]" + "(" + findEntityDesc(id) + ")\n"

                finialResult += resultStr
    return finialResult

def fibonacci(id,resultSet):
    result = findParentEntity(id)
    # print(f"父节点信息：{result}")
    if result :
        for item in result:
            if item["id"] is not None and item["id"] != "":
                resultSet.add(item["id"])
                fibonacci(item["id"], resultSet)

def getTotalName(id,name):
    result = findParentEntity(id)
    # print(f"父节点信息：{result}, 当前ID: {id}, 当前名称: {name}")  # 添加调试信息
    if result :
        if name != "":
            name = result[0]["name"] + "-" + name
        return getTotalName(result[0]["id"], name)
    return name

def findParentEntity(id):
    url = "http://**************:40436/relation_nzyxzrgs/gremlin"
    params = {"params": {"gremlin": "g.V("+str(id)+").in().toList()"}}
    response = requests.request("POST", url, data=json.dumps(params))
    dt = response.json()

    entitylist=[]
    for item in dt["result"]["data"]:
        entity={}
        entity["id"] = item["id"]
        entity["name"] = item["properties"]["name"][0]["value"]
        entitylist.append(entity)
    return entitylist

def findEntityName(id):
    url = "http://**************:40436/relation_nzyxzrgs/gremlin"
    # params = {"params": {"gremlin": "g.V("+id+")"}}
    params = {"params": {"gremlin": "g.V("+str(id)+")"}}
    response = requests.request("POST", url, data=json.dumps(params))
    dt = response.json()
    description = dt["result"]["data"][0]["properties"]["name"][0]["value"]
    return description

def findEntityDesc(id):
    url = "http://**************:40436/relation_nzyxzrgs/gremlin"
    # params = {"params": {"gremlin": "g.V("+id+")"}}
    params = {"params": {"gremlin": "g.V("+str(id)+")"}}
    response = requests.request("POST", url, data=json.dumps(params))
    dt = response.json()
    description = ""
    if dt["result"]["data"][0]["properties"].get("description") is not None:
        description = dt["result"]["data"][0]["properties"]["description"][0]["value"]
    return description

def answerbyGraph(query,graphcontent,chat_history):
    prompt = get_prompt("answer_template.txt",query,[])
    prompt = prompt.replace("{graphcontent}", graphcontent)
    response = callKimi(prompt)
    return response

def checkGraph(query,graphcontent):
    prompt = get_prompt("check_template.txt",query,[])
    prompt = prompt.replace("{graphcontent}", graphcontent)
    response = callKimi(prompt)
    return response

def answerbyGraph2(query):
    response = callKimi(query)
    return response

if __name__ == "__main__":
    while True:
        query = input("请输入: ")#.split()
        # flag = int(flag)
        chat_history =[]
        g_assistant_response = None
        status = None
        status_table = []
        txt = None
        print("等待模型的回答ing...")
        emotp, response = emocheck(query,chat_history)
        if emotp == 0 or emotp == 1:
            print(f"模型的回答1：{response}")

        print("机器人判定",emotp)
        # if emotp == 1 or emotp == 2: #咨询或 情绪
        #     # lst = getKeywords(query,chat_history)
        #     # txt = queryGraph(lst)
        #     ret = answerbyGraph(query,'',chat_history)
        #     print(f"模型的回答2：{ret}")
        #     continue
        
        if emotp == 2 or emotp == 3: #办理类
            lst = getKeywords(query,chat_history)
            print(f"提取出的关键词：{lst}")
            txt = queryGraph(lst)
            print(f"根据关键词获取到的图谱内容：{txt}")
            if txt == "" or txt is None:
                print("问题与谱图知识无关，请转人工！")
            else:
                checkTag = checkGraph(query,txt)
                print(f"checkTag:{checkTag}")
                if checkTag == "1":
                    ret = answerbyGraph(query,txt,chat_history)
                    print(f"模型的回答3：{ret}")
                else:
                    print("问题与谱图知识无关，请转人工！")
            continue

        if emotp == 1: #回复
            ret = answerbyGraph(query,'', chat_history)
            print(f"模型的回答3：{ret}")
            continue
            
        print(f"模型的回答4：{response}")
        




            