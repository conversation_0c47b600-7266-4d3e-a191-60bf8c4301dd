import json
import os

import pandas as pd
import requests

from tools import qwenMax, get_prompt, qwenMax2


def process_excel():
    parent_dir = os.path.dirname(os.getcwd())
    file_path = os.path.join(parent_dir, 'data', 'data5.xlsx')
    # 读取Excel文件
    df = pd.read_excel(file_path, engine='openpyxl',keep_default_na=False)
    # 遍历A列，拼接message
    for index, row in df.iterrows():
        # 获取A列的值作为问题
        question = row['faq_title']
        if(question):
            prompt = get_prompt("ch18.txt", question, "", "","")
            messages = [
                {"role": "user", "content": prompt}
            ]
            result = qwenMax2(messages)
            response = json.loads(result)
            print(response)
            df.at[index, '主体'] = response.get('主体类型')
            df.at[index, '意图'] = response.get('意图')
            df.at[index, '情形'] =response.get('情形')

    # 保存修改后的Excel文件
    df.to_excel(file_path, index=False, engine='openpyxl')




# 总控模块，在拆出业务问题时就拆出多个问题
if __name__ == '__main__':
    process_excel()