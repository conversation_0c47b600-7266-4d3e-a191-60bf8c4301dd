## 图谱检索相关的demo

import requests
import json

url = "http://**************:9001/relation_frzssk/gremlin"

def findEntityName(id):
    # params = {"params": {"gremlin": "g.V("+id+")"}}
    params = {"params": {"gremlin": "g.V("+id+")"}}
    response = requests.request("POST", url, data=json.dumps(params))
    dt = response.json()
    description = dt["result"]["data"][0]["properties"]["name"][0]["value"]
    return description


def findEntityDesc(id):
    # params = {"params": {"gremlin": "g.V("+id+")"}}
    params = {"params": {"gremlin": "g.V("+id+")"}}
    response = requests.request("POST", url, data=json.dumps(params))
    dt = response.json()
    description = dt["result"]["data"][0]["properties"]["description"][0]["value"]
    return description


def findParentEntity(id):
    params = {"params": {"gremlin": "g.V("+id+").in().toList()"}}
    response = requests.request("POST", url, data=json.dumps(params))
    dt = response.json()

    entitylist=[]
    for item in dt["result"]["data"]:
        entity={}
        entity["id"] = item["id"]
        entity["name"] = item["properties"]
        entitylist.append(entity)
    return entitylist

# print(findEntityName("4336"))
# print(findEntityDesc("32960"))
print(findParentEntity("106656"))
