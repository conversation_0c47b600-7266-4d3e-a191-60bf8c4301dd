import torch
from transformers import AutoModelForSeq2SeqLM, AutoTokenizer
from torch.quantization import quantize_dynamic
import time

def load_flan_t5_int8():
    # 使用原始的 T5 中文模型
    model = AutoModelForSeq2SeqLM.from_pretrained("uer/t5-base-chinese-cluecorpussmall")
    tokenizer = AutoTokenizer.from_pretrained("uer/t5-base-chinese-cluecorpussmall")

    # 不进行量化，先测试原始模型性能
    # model_int8 = quantize_dynamic(
    #     model,
    #     {torch.nn.Linear},
    #     dtype=torch.qint8
    # )

    return model, tokenizer

def get_model_size(model):
    param_size = 0
    for param in model.parameters():
        param_size += param.nelement() * param.element_size()
    buffer_size = 0
    for buffer in model.buffers():
        buffer_size += buffer.nelement() * buffer.element_size()
    
    size_all_mb = (param_size + buffer_size) / 1024**2
    return size_all_mb

class RAGContextRewriter:
    def __init__(self, model, tokenizer):
        self.model = model
        self.tokenizer = tokenizer
        self.conversation_history = []

    def add_conversation_turn(self, question, answer):
        self.conversation_history.append({
            'question': question,
            'answer': answer
        })

    def clear_history(self):
        self.conversation_history = []

    def rewrite_question(self, current_question, max_history=5, debug=False):
        if not self.conversation_history:
            return current_question

        recent_history = self.conversation_history[-max_history:]
        context_parts = []
        for turn in recent_history:
            context_parts.append(f"Q: {turn['question']} A: {turn['answer']}")

        context = " ".join(context_parts)
        # 使用更简单的 T5 格式
        prompt = f"问题改写: {current_question} 上下文: {context}"

        if debug:
            print(f"Prompt: {prompt}")

        inputs = self.tokenizer(prompt, return_tensors="pt", max_length=300, truncation=True)

        if debug:
            print(f"Input tokens: {inputs['input_ids'].shape}")

        # 移除 T5 模型不支持的参数
        model_inputs = {
            'input_ids': inputs['input_ids'],
            'attention_mask': inputs['attention_mask']
        }

        with torch.no_grad():
            outputs = self.model.generate(
                **model_inputs,
                max_new_tokens=30,
                num_beams=1,
                do_sample=False,
                pad_token_id=self.tokenizer.pad_token_id,
                eos_token_id=self.tokenizer.eos_token_id
            )

        full_output = self.tokenizer.decode(outputs[0], skip_special_tokens=True)
        if debug:
            print(f"Full output: {full_output}")
            print(f"Input length: {inputs['input_ids'].shape[1]}")
            print(f"Output length: {outputs[0].shape[0]}")

        input_text = self.tokenizer.decode(inputs['input_ids'][0], skip_special_tokens=True)
        if debug:
            print(f"Input text: {input_text}")

        if input_text in full_output:
            rewritten_question = full_output.replace(input_text, "").strip()
        else:
            if outputs[0].shape[0] > inputs['input_ids'].shape[1]:
                generated_tokens = outputs[0][inputs['input_ids'].shape[1]:]
                rewritten_question = self.tokenizer.decode(generated_tokens, skip_special_tokens=True).strip()
            else:
                rewritten_question = full_output.strip()

        if debug:
            print(f"Generated part: '{rewritten_question}'")
        if not rewritten_question or len(rewritten_question) < 3:
            return current_question

        return rewritten_question

def test_rewriter():
    print("正在加载模型...")
    model, tokenizer = load_flan_t5_int8()
    print(f"模型大小: {get_model_size(model):.2f} MB")

    rewriter = RAGContextRewriter(model, tokenizer)
    rewriter.clear_history()
    rewriter.add_conversation_turn("办理公卫许可证需要什么材料？", "你好，办理公卫许可证需要您带上本人、法人身份证，营业执照进行办理！")

    start_time = time.time()
    result2 = rewriter.rewrite_question("那我应该去哪里办？", 5, debug=True)
    end_time = time.time()

    print(f"原问题: 那我应该去哪里办？")
    print(f"改写后: {result2}")
    print(f"耗时: {end_time - start_time:.2f} 秒")

if __name__ == "__main__":
    test_rewriter()