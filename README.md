# Blbb Agent




# 企业办理，相关的材料
原始脑图：https://www.processon.com/mindmap/6759a6c251e6566236c6908f
新的图：https://docs.qq.com/sheet/DZkpkT3ZjV0d3VHNo?tab=09h059
存入本地的图库：http://172.29.255.220:40856/relation_nzyxzrgs/gremlin
存入ES索引：nzyxzrgs


测试用例：（全流程）
https://docs.qq.com/sheet/DZkVoTHliZkVCVElw?tab=9o3yeg

测试用例2：（多个问题）
https://docs.qq.com/sheet/DTlJNRWV6emJDbUFB?tab=BB08J2
/data/testyqdt.xlsx

# 律师数据
原始脑图：https://www.processon.com/v/674f0caa51e6566236b817ee
存入本地的图库：http://172.29.255.220:40856/relation_frzssk/gremlin
存入ES索引：frzssk


# 失业金数据
原始脑图：https://www.processon.com/v/673db3475b61580d259522ea



# 文件夹说明
steps: 分步执行相关
prompts:积累的提示词
combine:是合并调用的效果
data: 测试数据
prompts: 为steps和combine提供的提示词
prompts: jzm编写的提示词
prompt_sh : 是上海17岁小朋友 相关的提示词参考学习用




# 演示系统地址
https://zwblbb.epoint.com.cn:6443/epoint-blbb-web-page1/govsite/dist/#/main

http://192.168.219.30:8080/epoint-blbb-web/frame/fui/pages/themes/idea/idea?pageId=epointzsfw-idea
admin/Epoint@123

http://192.168.219.30:8080/epoint-blbb-web/aiagent/app-market

