import base64
import json
import re

import pandas as pd
import requests
import os
from tools import get_prompt,qwenMax2
#【深圳边聊边办】领域-主体类型-意图--情形提取测试

#领域识别
#def getDomain():
#主体类型、一级意图情形识别
def getPrincipaltype(question):
    prompt=""
    messages = [
        {"role": "user","content": prompt}
    ]
    data = {
        "messages": messages,
        "stream": False
    }
    response = qwenMax2(data)
    return json.loads(response['result']['content'])

#意图情形识别
def getIntensionAndCondition(question):
    intentarray = []
    conditionarray = []
    entityarray=[]

    #获取意图情形列表
    firsttypelist = getFisrtType()
    #获取主体类型
    datalist = getDetailType("1","")
    for item in datalist["entity_type"]:
        entityarray.append(item["entity_type_name"])

    for item in firsttypelist["klcategory_intent"]:
        intentarray.append(item["category_name"])

    for item in firsttypelist["klcategory_condition"]:
        conditionarray.append(item["category_name"])

    prompts = get_prompt("questionsplit_ztytqx.txt",question,str(entityarray),str(intentarray),str(conditionarray))
    messages = [
        {"role": "user", "content": prompts}
    ]
    firstcatagoryobj = qwenMax2(messages)
    # content_data = json.loads(firstcatagoryobj)
    # subjecttype=content_data.get('主体类型')
    # firstcondition = content_data.get('情形')
    # firstintent = content_data.get('意图')
    #
    # resultobj={}
    # resultobj["主体类型"] = subjecttype
    # resultobj["意图"] = firstintent
    # resultobj["情形"] = firstcondition
    return firstcatagoryobj,prompts

#意图情形识别
def getIntensionAndCondition2(question,firstcondition,firstintent):

    intentarray = []
    conditionarray = []
    if firstcondition!="":
        jsondata = getDetailType("3",firstcondition)
        for item in jsondata["condition"]:
            conditionarray.append(item["condition_name"])

    if firstintent != "":
        jsondata = getDetailType("2", firstintent)
        for item in jsondata["intent"]:
            intentarray.append(item["intent_name"])

    prompt = get_prompt("questionsplit_ztytqx.txt", question,"", str(intentarray), str(conditionarray))
    messages = [
        {"role": "user", "content": prompt}
    ]
    secondcatagoryobj = qwenMax2(messages)
    # response = {}
    # if isinstance(secondcatagoryobj, str) and is_json(secondcatagoryobj):
    #     response = json.loads(secondcatagoryobj)
    # intent = response.get('意图')
    # condition = response.get('情形')
    #
    # resultobj={}
    # resultobj["意图"] = intent
    # resultobj["情形"] = condition
    return secondcatagoryobj,prompt

def is_json(my_string):
    try:
        json_object = json.loads(my_string)
    except ValueError as e:
        return False
    return True

#获取一级分类
def getFisrtType():
    url="http://192.168.219.27:8080/qy-epoint-web/rest/klknowledgerest/getKlCategoryDataList"
    params={"privatekey":"1","category_type":"2,3","domain_id":"008d3fef-da26-11ef-9151-08bfb8b9824c"}
    headers = {
        'Content-Type': 'application/json'
    }

    response = requests.post(url, json=params, headers=headers)
    return json.loads(response.text)

#获取一级分类下的意图和情形
def getDetailType(category_type,category_name):
    url="http://192.168.219.27:8080/qy-epoint-web/rest/klknowledgerest/getKlDataList"
    params={"privatekey":"1","category_name":category_name,"category_type":category_type,"domain_id":"008d3fef-da26-11ef-9151-08bfb8b9824c"}
    headers = {
        'Content-Type': 'application/json'
    }

    response = requests.post(url, json=params, headers=headers)
    return json.loads(response.text)


def extract_json_from_string(text):
    # 使用正则表达式匹配被```json包裹的JSON数据[[7]]
    pattern = r'```json\n(.*?)\n```'
    matches = re.findall(pattern, text, re.DOTALL)

    results = []
    for match in matches:
        try:
            # 解析JSON字符串[[2, 6, 15]]
            json_data = json.loads(match.strip())
            results.append(json_data)
        except json.JSONDecodeError:
            continue

    return results[0] if results else None

def process_excel():
    parent_dir = os.path.dirname(os.getcwd())
    file_path = os.path.join(parent_dir, 'data', 'data4.xlsx')
    # 读取Excel文件
    df = pd.read_excel(file_path, engine='openpyxl',keep_default_na=False)
    # 遍历A列，拼接message
    for index, row in df.iterrows():
        # 获取A列的值作为问题
        question = row['原始问题']
        if(question):
            result1,prompts1 = getIntensionAndCondition(question)
            df.at[index, '分析1'] = result1
            json1 = extract_json_from_string(result1)
            if json1 is not None:
                df.at[index, '主体1'] = json1['主体类型']
                df.at[index, '意图1'] = json1['意图']
                df.at[index, '情形1'] =json1['情形']
            result2,prompts2 = getIntensionAndCondition2(question,json1['情形'],json1['意图'])
            df.at[index, '分析2'] = result2
            json2 = extract_json_from_string(result2)
            if json2 is not None:
                df.at[index, '意图2'] = json2['意图']
                df.at[index, '情形2'] = json2['情形']
            print(index)
            df.at[index, '提示词1'] = prompts1
            df.at[index, '提示词2'] = prompts2
    # 保存修改后的Excel文件
    df.to_excel(file_path, index=False, engine='openpyxl')

if __name__ == '__main__':
    process_excel()


