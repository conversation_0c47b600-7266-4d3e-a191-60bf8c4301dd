import datetime
import sys
import os
import time

# 添加项目根目录到 Python 路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from steps.tools import qwen3_sli_xt,qwen3


prompt = """
一、角色定位
你是一名资深的党政机关公文关键词提炼专家，擅长根据不同的公文写作类型和具体标题，精准提炼核心关键词，并围绕核心关键词发散出与标题紧密相关的写作关键词，助力公文写作内容的聚焦与拓展。全程严格遵循公文用词规范和行为准则。
二、任务及要求
1、接收用户输入信息
接收用户输入的公文写作类型：部门工作报告 和公文标题：部门年度pbc总结。
2、明确关键词生成依据
-深入分析写作类型的固有属性、常见内容范畴和表述特点，把握该类型公文的核心要素。
-精准剖析公文标题的核心主旨、关键对象和核心动作，提取标题中的核心信息点。
-结合写作类型和标题的关联，确保生成的关键词符合该类公文的写作语境和主题方向。
三、关键词生成要点
1、核心关键词提取
-从公文标题中精准提取能够高度概括公文主题、体现核心内容的词汇，核心关键词数量一般为 2-3 个，确保其能准确反映公文的核心要义。
2、发散关键词生成
-围绕核心关键词和写作类型，从公文写作的背景意义、工作任务、实施措施、责任主体、目标成效、相关政策等多个维度进行发散，数量一般为5-8个，确保发散关键词能够与公文主题和核心关键词有高度关联性
-背景意义类：结合写作类型特点，发散与标题主题相关的时代背景、政策背景、现实需求等方面的关键词。
-工作任务类：根据标题体现的工作方向，发散具体的工作事项、重点任务等相关关键词。
-实施措施类：围绕实现标题目标所需的手段，发散具体的方法、举措、途径等关键词。
-其他相关类：根据写作类型和标题的特殊性，适当发散责任部门、时间要求、保障机制等方面的关键词。
四、行为准则
1、生成的关键词必须与公文写作类型和标题紧密相关，避免出现无关或偏离主题的词汇。
2、关键词用词需规范、准确，符合公文写作的官方表述习惯，避免使用口语化、随意化的词汇。
3、核心关键词要精准精炼，发散关键词要具有一定的拓展性和实用性，能为公文写作提供思路启发。
4、生成的关键词数量要适宜，核心关键词 2-3 个，发散关键词 5-8 个。
五、输出规范
必须严格按照如下JSON格式输出，不要包含其他内容，输出结果不要用代码块包起来：

// 单个关键字不超过10个字，可以返回多个，最多10个。

{
    "keyword": [
        {
            "title": "关键字1，不超过10个字"
        },
        {
            "title": "关键字2，不超过10个字"
        },
        {
            "title": "关键字3，不超过10个字"
        }
    ]
}
"""
messages=[{
    "role":"user",
    "content":prompt
}]

# 记录开始时间
start_time = time.time()

print("开始执行意图识别分析...")
result = qwen3(messages)

# 记录结束时间
end_time = time.time()

# 计算执行时间
execution_time = end_time - start_time

print(f"分析结果: {result}")
print(f"执行时间: {execution_time:.2f} 秒")

