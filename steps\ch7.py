#TODO  批量刷一下kimi回答企业问题的效果

# 读取data/testqudt.xlsx文件，取得问题，调用kimy接口，将答案写回C表
# 按排业务人员 判断答案是否准确。

# 以此考虑A和B类 是否要分离

import os
import json

import openpyxl
import requests
import base64


url = "http://192.168.186.18:8088/llm/predict"
def generate_files():
    parent_dir = os.path.dirname(os.getcwd())
    # 构造最终的文件路径
    file_path = [os.path.join(parent_dir, 'data', '事件_市场主体登记.txt') , os.path.join(parent_dir, 'data', '生产_内资有限责任公司登记.txt')]
    files = []

    # 遍历 file_path 列表中的每个文件
    for path in file_path:
        with open(path, "rb") as f:
            file_stream = f.read()
        file_b64 = base64.b64encode(file_stream).decode("utf-8")
        files.append(file_b64)

    return files

def call_api(files, content , stream=True):
    cache_tag = "kimi_graphrag"
    content = f"""
    请不要查询任何联网信息，也不需要调用大模型自己已有的知识储备，也不能对问题描述的情形做任何推测，只能严格按照刚才上传的知识图谱的节点的description属性回答问题。回答问题时请直接给出答案，无需给出分析过程。如果没有找到答案，请直接回复“尚未准备好相关知识，请拨打电话0755-88127587咨询。”
    开始问问题：{content}
    """
    messages = [
        {"role": "user", "content": content},
    ]

    data = {
        "messages": messages,
        "cache_tag": cache_tag,
        "stream": stream
    }

    if files:
        data["files"] = files

    response = requests.request("POST", url, data=json.dumps(data), stream=stream)
    if not stream:
        return json.loads(response.text)['result']
    else:
        for chunk in response.iter_lines():
            print(chunk.decode("utf-8"))

if __name__ == "__main__":
    # files = generate_files()
    files = []
    # print(call_api(files , '企业通过深圳市场监管局网站办理该内资企业简易注销登记，在“待办企业信息”页面填写了企业相关信息后点击“下一步”，系统提示：简易注销调用接口异常。该情况下无法操作进入下一步骤，现咨询该情况如何处理？' , False))
    xlsx_path = os.path.join(os.path.dirname(os.getcwd()), 'data', 'testyqdt.xlsx')
    wb = openpyxl.load_workbook(xlsx_path)
    sheet = wb.active
    original_question_col = 2
    answer_col = 4
    for row in sheet.iter_rows(min_row=2, min_col=original_question_col, max_col=original_question_col):
        question = row[0].value
        if question:
            print("正在回复问题：" + question)
            answer = call_api(files, question, False)
            sheet.cell(row=row[0].row, column=answer_col, value=answer)
        else:
            break
    wb.save(xlsx_path)