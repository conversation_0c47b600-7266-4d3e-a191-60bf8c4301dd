#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Qwen3大模型工具函数
提供简单的API调用接口
"""

import json
import requests
import random
from typing import Iterator, Dict, Any
from dashscope import Generation


def call_qwen3_stream(messages: list, api_key: str = "sk-c342324d10474284abc452d71d0dd105") -> Iterator[str]:
    """
    调用Qwen3模型进行流式输出
    
    Args:
        messages: 消息列表，格式为 [{"role": "user", "content": "问题"}]
        api_key: API密钥
        
    Yields:
        str: 流式输出的文本片段
    """
    try:
        seed = random.randint(1, 10000)
        response = Generation.call(
            model="qwen3-32b",
            messages=messages,
            api_key=api_key,
            seed=seed,
            result_format='message',
            temperature=0.3,
            enable_thinking=False,
            stream=True,
            stop='Observation'
        )
        
        # 处理流式响应
        for chunk in response:
            if hasattr(chunk, 'output') and hasattr(chunk.output, 'choices'):
                if chunk.output.choices and len(chunk.output.choices) > 0:
                    choice = chunk.output.choices[0]
                    if hasattr(choice, 'message') and hasattr(choice.message, 'content'):
                        content = choice.message.content
                        if content:
                            yield content
                    elif hasattr(choice, 'delta') and hasattr(choice.delta, 'content'):
                        content = choice.delta.content
                        if content:
                            yield content
                            
    except Exception as e:
        yield f"调用模型时发生错误: {str(e)}"


def call_qwen3(messages: list, api_key: str = "sk-c342324d10474284abc452d71d0dd105") -> str:
    """
    调用Qwen3模型（非流式）
    
    Args:
        messages: 消息列表，格式为 [{"role": "user", "content": "问题"}]
        api_key: API密钥
        
    Returns:
        str: 模型回答
    """
    try:
        seed = random.randint(1, 10000)
        response = Generation.call(
            model="qwen3-32b",
            messages=messages,
            api_key=api_key,
            seed=seed,
            result_format='message',
            temperature=0.3,
            enable_thinking=False,
            stream=False,
            stop='Observation'
        )
        
        return response.output.choices[0]['message']['content']
        
    except Exception as e:
        return f"调用模型时发生错误: {str(e)}"


def create_government_prompt(user_question: str) -> list:
    """
    创建政务咨询的消息格式
    
    Args:
        user_question: 用户问题
        
    Returns:
        list: 格式化的消息列表
    """
    system_prompt = """##职责 
你是一名资深政务顾问AI。你的核心使命仅仅结合我提供的"专用知识"回答用户的问题，不允许使用预训练的知识以及"专用知识"以外的任何知识。
你需要为用户提供关于政策法规、办事流程、民生服务等领域的权威、详尽且富有同理心的解答。
只允许根据"专用知识"字面内容进行回答，不允许联想、搜索

##要求
1、从现在起，你只能依据我提供给你的**我的知识**来回答问题。在回答任何问题时，不要使用你自身原本储备的任何其他知识来作答。请严格遵守这一规则。
2、答案中提到的地点、电话、材料、方式等，如果**我的知识**中有相关内容，以我的知识为准，并按原文知识逐条输出。
3、禁止在回复内容中列出知识条目！！！
4、回答中不得出现知识引用文本： "根据【我的知识】"、"参考知识库第X条"、"详见法规第X条"、 "根据知识库"、"根据知识"、"根据《xxx》第x条"、"根据xxx"、"（详见知识[x]）"、"（知识[x]）"、"（知识[x]第x条）"、"（第x条）"。请务必遵守此要求。
5、回答的章节内容需要逻辑清晰
6、要求输出简明扼要，不冗余
7、链接地址请严格按照格式输出！！！：江苏土地市场网（<a href="http://www.landjs.com" target="_blank" rel="noopener noreferrer">http://www.landjs.com</a>）
8、在回答的最后另起两行增加回复内容"以上内容由AI智能生成，可供参考。"。

##我的知识
1. 缓缴养老保险费对员工退休待遇的影响：
职工办理退休前需缴清社会保险费，方可办理退休手续。基本养老金根据个人累计缴费年限、缴费工资、当地职工平均工资、个人账户金额、城镇人口平均预期寿命等因素确定。

2. 退休后指南：
一、企业职工及灵活就业人员办理退休
办理事项：企业职工基本养老保险退休审批
（一）、申请条件
需满足以下条件：
1.年龄条件：（1）正常退休年龄：从2025年1月1日起，男职工和原法定退休年龄为五十五周岁的女职工，法定退休年龄每四个月延迟一个月，分别逐步延迟至六十三周岁和五十八周岁；原法定退休年龄为五十周岁的女职工，法定退休年龄每二个月延迟一个月，逐步延迟至五十五周岁。国家另有规定的，从其规定；（2）弹性提前退休年龄：职工达到最低缴费年限，可以自愿选择弹性提前退休，提前时间最长不超过三年，且退休年龄不得低于女职工五十周岁、五十五周岁及男职工六十周岁的原法定退休年龄；（3）弹性延迟退休年龄：职工达到法定退休年龄，所在单位与职工协商一致的，可以弹性延迟退休，延迟时间最长不超过三年。国家另有规定的，从其规定。 
2.缴费条件：从2030年1月1日起，将职工按月领取基本养老金最低缴费年限由十五年逐步提高至二十年，每年提高六个月。 《江苏省企业职工基本养老保险规定》（省政府令第146号）第23条：参保人员依法参加基本养老保险，达到国家和省规定的退休年龄且缴费年限满足国家规定最低缴费年限的，按月领取基本养老金。《全国人民代表大会常务委员会关于实施渐进式延迟法定退休年龄的决定》、《国务院关于渐进式延迟法定退休年龄的办法》。
（二）、申请材料
1.居民身份证件/护照
2.社会保障卡
3.江苏省企业职工退休审批表
4.职工个人档案
5.一寸照片一张
6.企业职工基本养老保险退休时间申请书
7.退休办理需要的其他必要材料等
（三）、办理地址
线上办理地址：登录江苏省人力资源和社会保障厅网上办事服务大厅（<a href="https://rs.jshrss.jiangsu.gov.cn/index/" target="_blank" rel="noopener noreferrer">https://rs.jshrss.jiangsu.gov.cn/index/</a>），申请后线下审核。
线下办理地址：江苏省高邮市海潮东路997号 高邮市政务服务中心二楼D区人社退休一件事窗口，咨询电话：0514-84642027。
（四）、办理流程
1.线上办理流程：
登录江苏省人力资源和社会保障厅网上办事服务大厅（<a href="https://rs.jshrss.jiangsu.gov.cn/index/" target="_blank" rel="noopener noreferrer">https://rs.jshrss.jiangsu.gov.cn/index/</a>），选择企业职工基本养老保险退休申请事项后，下载并打印申请表，并按照业务办理的指南准备的相关业务材料，前往人社服务大厅现场取号审核办理。
2.线下办理流程：
申请人按照业务办理的指南准备业务的相关材料，前往人社服务大厅现场取号办理。
（五）、办理时限
法定办结时限：20个工作日
（六）、办理结果
企业职工退休审批表
（七）、收费标准
不收费。

四、退休后相关事宜
1、养老金发放时间：机关事业单位退休金每月10日左右发放；企业职工退休金每月15日左右发放。
2、养老金年度认证资格认证方式：可以通过江苏智慧人社APP或微信小程序进行待遇资格认证。
3、退休人员享受医保待遇缴费年限：医疗保险缴费年限要求因参保类型（职工医保或城乡居民医保）而异，参加职工基本医疗保险累计缴费年限（包含按照国家规定认可的视同缴费年限和实际缴费年限）男性满二十五年、女性满二十年；城乡居民医保无缴费年限要求，需要每年缴费才能享受医保待遇。
4、若您有公积金，退休后您可至公积金窗口咨询公积金余额提取事宜"""

    return [
        {"role": "system", "content": system_prompt},
        {"role": "user", "content": user_question}
    ]


def government_qa_stream(user_question: str) -> Iterator[str]:
    """
    政务问答流式输出
    
    Args:
        user_question: 用户问题
        
    Yields:
        str: 流式输出的回答片段
    """
    messages = create_government_prompt(user_question)
    yield from call_qwen3_stream(messages)


def government_qa(user_question: str) -> str:
    """
    政务问答（非流式）
    
    Args:
        user_question: 用户问题
        
    Returns:
        str: 完整回答
    """
    messages = create_government_prompt(user_question)
    return call_qwen3(messages)


# 示例使用
if __name__ == "__main__":
    import time
    
    question = "我老了怎么办"
    
    print("=== 流式输出示例 ===")
    print(f"问题: {question}")
    print("回答: ", end="")
    
    for chunk in government_qa_stream(question):
        print(chunk, end="", flush=True)
        time.sleep(0.01)  # 模拟打字效果
    
    print("\n\n=== 非流式输出示例 ===")
    print(f"问题: {question}")
    print("回答:")
    response = government_qa(question)
    print(response)
