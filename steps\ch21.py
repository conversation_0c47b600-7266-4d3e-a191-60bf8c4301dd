# 将用户的问题，拆分为寒喧、情绪、业务问题

# TODO 调一调提示词

from tools import get_prompt16, qwenMax,deepseek7b,qwen7b, qwenMax2
from ch19 import call_tx_hunyuan
question = "毕业后进入深圳一家外企工作，户籍档案则调入“深圳人才集团”，现在我没有工作，在深圳也没有房产，但我在深圳罗湖区续保了“灵活就业社保”，要求我迁出户籍，请问我的户籍可以迁往何处"
messages = [
        {
            "role": "user",
            "content": get_prompt16("ch21.txt",question,"","")
        }
    ]
response = qwenMax2(messages)

print(response)

#总控模块，在拆出业务问题时就拆出多个问题