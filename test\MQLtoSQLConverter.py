import json
from typing import List, Dict, Any

class MQLtoSQLConverter:
    def _build_select(self, metric: Dict[str, Any]) -> str:
        expr = metric.get('expression', '')
        alias = metric['metric']
        return f"{expr} AS `{alias}`"

    def _build_where(self, filters: List[Dict[str, Any]], time_filter: Dict[str, Any]) -> str:
        clauses = []
        for f in filters:
            field = f['field']
            op = f['operator']
            vals = f['value']
            if isinstance(vals, list):
                vals_sql = ', '.join(f"'{v}'" for v in vals)
                clauses.append(f"{field} {op} ({vals_sql})")
            else:
                clauses.append(f"{field} {op} '{vals}'")
        if time_filter:
            tf = time_filter
            field = tf['field']
            start = tf['start_date']
            end = tf['end_date']
            clauses.append(f"{field} BETWEEN '{start}' AND '{end}'")
        return ' AND '.join(clauses) if clauses else ''

    def _build_group_by(self, group_by: List[str]) -> str:
        return ', '.join(group_by)

    def _build_order_by(self, sort: Dict[str, str]) -> str:
        if not sort:
            return ''
        field = sort.get('field')
        order = sort.get('order', 'ASC').upper()
        if not field:
            return ''
        return f"ORDER BY {field} {order}"

    def convert(self, mql: List[Dict[str, Any]]) -> List[str]:
        sql_statements = []
        for q in mql:
            table = q.get('table')
            select_clause = self._build_select(q)
            from_clause = f"FROM {table}"
            where = self._build_where(q.get('filters', []), q.get('time_filter', {}))
            where_clause = f"WHERE {where}" if where else ''
            group_by = q.get('group_by', [])
            group_clause = f"GROUP BY {self._build_group_by(group_by)}" if group_by else ''
            order_clause = self._build_order_by(q.get('sort', {}))

            sql = f"SELECT {select_clause} {from_clause} {where_clause} {group_clause} {order_clause}"
            sql_statements.append(sql.strip())
        return sql_statements

if __name__ == '__main__':
    sample_mql = '''
    [
      {
        "metric": "工程类项目成交数量",
        "table": "政府采购立方体",
        "expression": "SUM(项目数量)",
        "aggregate": "SUM",
        "filters": [
          {"field": "项目类别", "operator": "IN", "value": ["工程类"]}
        ],
        "group_by": [],
        "time_filter": {"field": "中标时间", "start_date": "2024-01-01", "end_date": "2024-12-31"},
        "sort": {"field": "项目数量", "order": "DESC"}, 
        "chart_type": "line"
      }
    ]
    '''
    mql_list = json.loads(sample_mql)
    converter = MQLtoSQLConverter()
    for stmt in converter.convert(mql_list):
        print(stmt)
